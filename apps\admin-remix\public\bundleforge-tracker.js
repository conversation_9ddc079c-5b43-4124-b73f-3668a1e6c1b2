/**
 * BundleForge Analytics Tracker
 * 
 * This script tracks bundle interactions and enables A/B testing functionality.
 * Include this script in your theme to enable analytics tracking.
 * 
 * Usage:
 * <script src="/apps/bundleforge/bundleforge-tracker.js" defer></script>
 */

(function() {
  'use strict';

  // Configuration
  const TRACKER_VERSION = '1.0.0';
  const API_ENDPOINT = '/apps/bundleforge/api/track';
  const SESSION_KEY = 'bundleforge_session';
  const AB_TEST_KEY = 'bundleforge_ab_tests';
  
  // Initialize tracker
  const BundleForgeTracker = {
    session: null,
    abTests: {},
    
    init: function() {
      this.session = this.getOrCreateSession();
      this.abTests = this.loadABTests();
      this.setupEventListeners();
      this.trackPageView();
    },
    
    getOrCreateSession: function() {
      let session = sessionStorage.getItem(SESSION_KEY);
      if (!session) {
        session = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem(SESSION_KEY, session);
      }
      return session;
    },
    
    loadABTests: function() {
      const stored = localStorage.getItem(AB_TEST_KEY);
      return stored ? JSON.parse(stored) : {};
    },
    
    saveABTests: function() {
      localStorage.setItem(AB_TEST_KEY, JSON.stringify(this.abTests));
    },
    
    getCustomerId: function() {
      // Try to get customer ID from various sources
      if (window.ShopifyAnalytics && window.ShopifyAnalytics.meta && window.ShopifyAnalytics.meta.customerId) {
        return window.ShopifyAnalytics.meta.customerId;
      }
      if (window.__st && window.__st.cid) {
        return window.__st.cid;
      }
      // Check meta tags
      const customerMeta = document.querySelector('meta[name="customer-id"]');
      if (customerMeta) {
        return customerMeta.content;
      }
      return null;
    },
    
    getShopDomain: function() {
      if (window.Shopify && window.Shopify.shop) {
        return window.Shopify.shop;
      }
      // Fallback to current domain
      return window.location.hostname;
    },
    
    trackEvent: function(eventType, bundleId, additionalData = {}) {
      const customerId = this.getCustomerId();
      const shopDomain = this.getShopDomain();
      
      const eventData = {
        event_type: eventType,
        bundle_id: bundleId,
        customer_id: customerId,
        session_id: this.session,
        metadata: {
          ...additionalData,
          tracker_version: TRACKER_VERSION,
          page_url: window.location.href,
          referrer: document.referrer,
          user_agent: navigator.userAgent,
          ab_test_enabled: true
        }
      };
      
      // Add revenue if this is a purchase event
      if (eventType === 'bundle_purchase' && additionalData.revenue) {
        eventData.revenue = additionalData.revenue;
      }
      
      // Send tracking request
      fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Shop': shopDomain,
          'X-Shopify-Customer-Id': customerId || ''
        },
        body: JSON.stringify(eventData)
      })
      .then(response => response.json())
      .then(data => {
        // Store A/B test assignment if returned
        if (data.ab_test && data.ab_test.test_id) {
          this.abTests[bundleId] = {
            test_id: data.ab_test.test_id,
            variant: data.ab_test.variant,
            assigned_at: new Date().toISOString()
          };
          this.saveABTests();
          
          // Fire custom event for theme to react to variant assignment
          window.dispatchEvent(new CustomEvent('bundleforge:ab-test-assigned', {
            detail: {
              bundle_id: bundleId,
              test_id: data.ab_test.test_id,
              variant: data.ab_test.variant
            }
          }));
        }
      })
      .catch(error => {
        console.error('BundleForge tracking error:', error);
      });
    },
    
    trackPageView: function() {
      // Check if we're on a product page with bundle data
      const bundleElements = document.querySelectorAll('[data-bundleforge-id]');
      bundleElements.forEach(element => {
        const bundleId = element.getAttribute('data-bundleforge-id');
        if (bundleId) {
          this.trackEvent('bundle_view', bundleId);
        }
      });
    },
    
    setupEventListeners: function() {
      const self = this;
      
      // Track add to cart events
      document.addEventListener('click', function(event) {
        const button = event.target.closest('[data-bundleforge-add-to-cart]');
        if (button) {
          const bundleId = button.getAttribute('data-bundleforge-id');
          if (bundleId) {
            self.trackEvent('bundle_add_to_cart', bundleId);
          }
        }
      });
      
      // Track bundle purchase completion
      if (window.location.pathname.includes('/thank_you') || 
          window.location.pathname.includes('/orders/')) {
        // Look for bundle data in order confirmation
        const orderData = this.extractOrderData();
        if (orderData && orderData.bundles) {
          orderData.bundles.forEach(bundle => {
            this.trackEvent('bundle_purchase', bundle.id, {
              revenue: bundle.revenue,
              order_id: orderData.order_id
            });
          });
        }
      }
      
      // Listen for AJAX add to cart events (common in modern themes)
      const originalFetch = window.fetch;
      window.fetch = function(...args) {
        const [url, options] = args;
        
        if (url.includes('/cart/add') && options && options.method === 'POST') {
          // Try to extract bundle information from the request
          try {
            const body = JSON.parse(options.body);
            if (body.properties && body.properties._bundle_id) {
              self.trackEvent('bundle_add_to_cart', body.properties._bundle_id);
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
        
        return originalFetch.apply(this, args);
      };
    },
    
    extractOrderData: function() {
      // Try to extract order data from various sources
      if (window.Shopify && window.Shopify.checkout) {
        const checkout = window.Shopify.checkout;
        const bundles = [];
        let totalRevenue = 0;
        
        // Look for bundle line items
        if (checkout.line_items) {
          checkout.line_items.forEach(item => {
            if (item.properties && item.properties._bundle_id) {
              bundles.push({
                id: item.properties._bundle_id,
                revenue: parseFloat(item.price) * item.quantity / 100
              });
              totalRevenue += parseFloat(item.price) * item.quantity / 100;
            }
          });
        }
        
        if (bundles.length > 0) {
          return {
            order_id: checkout.order_id || checkout.token,
            bundles: bundles,
            total_revenue: totalRevenue
          };
        }
      }
      
      return null;
    },
    
    // Public API for theme developers
    getABTestVariant: function(bundleId) {
      if (this.abTests[bundleId]) {
        return this.abTests[bundleId].variant;
      }
      return null;
    },
    
    isABTestActive: function(bundleId) {
      return !!this.abTests[bundleId];
    }
  };
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      BundleForgeTracker.init();
    });
  } else {
    BundleForgeTracker.init();
  }
  
  // Expose to global scope for theme access
  window.BundleForgeTracker = BundleForgeTracker;
})();