import { useCallback } from 'react';
import { useFetcher } from '@remix-run/react';
import { authenticatedApiCall } from '~/libs/app-bridge.client';

export function useAuthenticatedFetch() {
  const fetcher = useFetcher();
  
  const authenticatedFetch = useCallback(async (
    url: string,
    options: RequestInit = {}
  ) => {
    try {
      const response = await authenticatedApiCall(url, options);
      
      if (!response.ok) {
        throw new Error(`Request failed: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Authenticated fetch error:', error);
      throw error;
    }
  }, []);
  
  return {
    fetch: authenticatedFetch,
    fetcher,
  };
}