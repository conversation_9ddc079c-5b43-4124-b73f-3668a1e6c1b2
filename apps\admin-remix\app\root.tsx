import {
  <PERSON>s,
  <PERSON>Reload,
  Met<PERSON>,
  Outlet,
  <PERSON>ripts,
  ScrollRestoration,
  useLoaderData,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs, HeadersFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { AppProvider } from "@shopify/polaris";
import { Provider as AppBridgeProvider } from "@shopify/app-bridge-react";
import "@shopify/polaris/build/esm/styles.css";
import { useEffect } from "react";
import { initializeAppBridge } from "~/libs/app-bridge.client";
import { createSecurityHeaders } from "~/libs/security-headers.server";
import { env } from "~/libs/env-encryption.server";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const host = url.searchParams.get("host");

  return json({
    apiKey: env.SHOPIFY_API_KEY || "",
    shop: shop || "",
    host: host || "",
    isEmbedded: !!(shop && host), // Only embedded if both shop and host are present
  });
}

export const headers: HeadersFunction = createSecurityHeaders;

export default function App() {
  const { apiKey, shop, host, isEmbedded } = useLoaderData<typeof loader>();
  
  useEffect(() => {
    if (isEmbedded && apiKey && shop && host) {
      // Initialize App Bridge for client-side usage
      initializeAppBridge({
        apiKey,
        shopOrigin: shop,
        host,
        forceRedirect: true,
      });
    }
  }, [apiKey, shop, host, isEmbedded]);
  
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {isEmbedded ? (
          <AppBridgeProvider
            config={{
              apiKey,
              shop,
              host,
              forceRedirect: true,
            }}
          >
            <AppProvider
              i18n={{
                Polaris: {
                  Avatar: {
                    label: "Avatar",
                    labelWithInitials: "Avatar with initials {initials}",
                  },
                  ContextualSaveBar: {
                    save: "Save",
                    discard: "Discard",
                  },
                  TextField: {
                    characterCount: "{count} characters",
                  },
                  TopBar: {
                    toggleMenuLabel: "Toggle menu",
                    SearchField: {
                      clearButtonLabel: "Clear",
                      search: "Search",
                    },
                  },
                  Common: {
                    checkbox: "checkbox",
                  },
                },
              }}
            >
              <Outlet />
            </AppProvider>
          </AppBridgeProvider>
        ) : (
          <AppProvider
            i18n={{
              Polaris: {
                Avatar: {
                  label: "Avatar",
                  labelWithInitials: "Avatar with initials {initials}",
                },
                ContextualSaveBar: {
                  save: "Save",
                  discard: "Discard",
                },
                TextField: {
                  characterCount: "{count} characters",
                },
                TopBar: {
                  toggleMenuLabel: "Toggle menu",
                  SearchField: {
                    clearButtonLabel: "Clear",
                    search: "Search",
                  },
                },
                Common: {
                  checkbox: "checkbox",
                },
              },
            }}
          >
            <Outlet />
          </AppProvider>
        )}
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}