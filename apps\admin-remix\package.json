{"name": "admin-remix", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build", "dev": "remix dev --manual", "start": "remix-serve ./build/index.js", "typecheck": "tsc", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@remix-run/node": "^2.0.0", "@remix-run/react": "^2.0.0", "@remix-run/serve": "^2.0.0", "@shopify/admin-api-client": "^1.0.0", "@shopify/app-bridge": "^3.7.0", "@shopify/app-bridge-react": "^3.7.0", "@shopify/polaris": "^12.0.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "isomorphic-dompurify": "^2.25.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "redis": "^4.6.0", "zod": "^3.22.0"}, "devDependencies": {"@remix-run/dev": "^2.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "msw": "^1.3.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vitest": "^0.34.0"}, "engines": {"node": ">=16.0.0"}}