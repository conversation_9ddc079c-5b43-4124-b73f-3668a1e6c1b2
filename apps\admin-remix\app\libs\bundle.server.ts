import { createShopifyClient } from "./shopify.server";
import { cache, CACHE_TTL } from "./cache.server";
import type { BundleConfig, BundleProduct } from "./metafield.server";

// Metafield namespace for bundle configurations
const BUNDLE_NAMESPACE = "bundleforge";
const BUNDLE_KEY = "bundle_config";

/**
 * Bundle service using Shopify metafields for data storage
 * This replaces the Supabase implementation with native Shopify storage
 */
export class BundleService {
  private shopifyClient: any;
  private shop: string;

  constructor(shop: string, accessToken: string) {
    this.shopifyClient = createShopifyClient(shop, accessToken);
    this.shop = shop;
  }

  /**
   * Create a new bundle configuration stored as a shop metafield
   */
  async createBundle(bundle: Omit<BundleConfig, 'id' | 'created_at' | 'updated_at'>): Promise<BundleConfig> {
    const bundleId = `bundle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    const bundleConfig: BundleConfig = {
      ...bundle,
      id: bundleId,
      created_at: now,
      updated_at: now,
      shop_domain: this.shop,
    };

    // Store bundle configuration in shop metafield
    const mutation = `
      mutation metafieldSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      metafields: [
        {
          namespace: BUNDLE_NAMESPACE,
          key: `${BUNDLE_KEY}_${bundleId}`,
          value: JSON.stringify(bundleConfig),
          type: "json",
          ownerId: `gid://shopify/Shop/${this.shop}`,
        },
      ],
    };

    const response = await this.shopifyClient.request(mutation, { variables });

    if (response.data?.metafieldsSet?.userErrors?.length > 0) {
      throw new Error(`Failed to create bundle: ${response.data.metafieldsSet.userErrors[0].message}`);
    }

    // Invalidate bundle cache
    await this.invalidateBundleCache();
    
    // Sync bundles to shop metafields for Shopify Functions
    await this.syncBundlesToShopMetafields();

    return bundleConfig;
  }

  /**
   * Get a bundle by ID from shop metafields
   */
  async getBundleById(bundleId: string): Promise<BundleConfig | null> {
    const query = `
      query getShopMetafield($namespace: String!, $key: String!) {
        shop {
          metafield(namespace: $namespace, key: $key) {
            value
          }
        }
      }
    `;

    const variables = {
      namespace: BUNDLE_NAMESPACE,
      key: `${BUNDLE_KEY}_${bundleId}`,
    };

    const response = await this.shopifyClient.request(query, { variables });

    if (!response.data?.shop?.metafield?.value) {
      return null;
    }

    try {
      return JSON.parse(response.data.shop.metafield.value);
    } catch (error) {
      console.error('Failed to parse bundle configuration:', error);
      return null;
    }
  }

  /**
   * Update a bundle configuration
   */
  async updateBundle(bundleId: string, updates: Partial<BundleConfig>): Promise<BundleConfig> {
    const existingBundle = await this.getBundleById(bundleId);
    if (!existingBundle) {
      throw new Error(`Bundle with ID ${bundleId} not found`);
    }

    const updatedBundle: BundleConfig = {
      ...existingBundle,
      ...updates,
      id: bundleId, // Ensure ID doesn't change
      updated_at: new Date().toISOString(),
    };

    const mutation = `
      mutation metafieldSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      metafields: [
        {
          namespace: BUNDLE_NAMESPACE,
          key: `${BUNDLE_KEY}_${bundleId}`,
          value: JSON.stringify(updatedBundle),
          type: "json",
          ownerId: `gid://shopify/Shop/${this.shop}`,
        },
      ],
    };

    const response = await this.shopifyClient.request(mutation, { variables });

    if (response.data?.metafieldsSet?.userErrors?.length > 0) {
      throw new Error(`Failed to update bundle: ${response.data.metafieldsSet.userErrors[0].message}`);
    }

    // Invalidate bundle cache
    await this.invalidateBundleCache();
    
    // Sync bundles to shop metafields for Shopify Functions
    await this.syncBundlesToShopMetafields();

    return updatedBundle;
  }

  /**
   * Delete a bundle configuration
   */
  async deleteBundle(bundleId: string): Promise<void> {
    // First, get the metafield ID
    const query = `
      query getShopMetafield($namespace: String!, $key: String!) {
        shop {
          metafield(namespace: $namespace, key: $key) {
            id
          }
        }
      }
    `;

    const response = await this.shopifyClient.request(query, {
      variables: {
        namespace: BUNDLE_NAMESPACE,
        key: `${BUNDLE_KEY}_${bundleId}`,
      },
    });

    const metafieldId = response.data?.shop?.metafield?.id;

    if (metafieldId) {
      const deleteMutation = `
        mutation metafieldDelete($input: MetafieldDeleteInput!) {
          metafieldDelete(input: $input) {
            deletedId
            userErrors {
              field
              message
            }
          }
        }
      `;

      const deleteResponse = await this.shopifyClient.request(deleteMutation, {
        variables: {
          input: { id: metafieldId },
        },
      });

      if (deleteResponse.data?.metafieldDelete?.userErrors?.length > 0) {
        throw new Error(`Failed to delete bundle: ${deleteResponse.data.metafieldDelete.userErrors[0].message}`);
      }
    }

    // Invalidate bundle cache
    await this.invalidateBundleCache();
    
    // Sync bundles to shop metafields for Shopify Functions
    await this.syncBundlesToShopMetafields();
  }

  /**
   * Delete multiple bundles in batch for better performance
   */
  async deleteBundlesBatch(bundleIds: string[]): Promise<void> {
    // Get metafield IDs for all bundles
    const metafieldIds: string[] = [];
    
    // Fetch metafield IDs in parallel
    const metafieldQueries = bundleIds.map(bundleId => {
      const query = `
        query getShopMetafield($namespace: String!, $key: String!) {
          shop {
            metafield(namespace: $namespace, key: $key) {
              id
            }
          }
        }
      `;

      return this.shopifyClient.request(query, {
        variables: {
          namespace: BUNDLE_NAMESPACE,
          key: `${BUNDLE_KEY}_${bundleId}`,
        },
      }).then(response => ({
        bundleId,
        metafieldId: response.data?.shop?.metafield?.id,
      }));
    });

    const results = await Promise.all(metafieldQueries);
    
    for (const result of results) {
      if (result.metafieldId) {
        metafieldIds.push(result.metafieldId);
      }
    }

    // Delete in batches of 10 for optimal performance
    const batchSize = 10;
    for (let i = 0; i < metafieldIds.length; i += batchSize) {
      const batch = metafieldIds.slice(i, i + batchSize);
      
      // Use Promise.all for parallel deletion within batch
      await Promise.all(
        batch.map(id => {
          const deleteMutation = `
            mutation metafieldDelete($input: MetafieldDeleteInput!) {
              metafieldDelete(input: $input) {
                deletedId
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          return this.shopifyClient.request(deleteMutation, {
            variables: {
              input: { id },
            },
          });
        })
      );
    }

    // Invalidate bundle cache
    await this.invalidateBundleCache();
    
    // Sync bundles to shop metafields for Shopify Functions
    await this.syncBundlesToShopMetafields();
    
    console.log(`Deleted ${metafieldIds.length} bundles in batch`);
  }

  /**
   * Invalidate all bundle caches for the shop
   */
  private async invalidateBundleCache(): Promise<void> {
    await cache.invalidatePattern(`bundles:${this.shop}:*`, 'bundles');
  }

  /**
   * Get all active bundles for the shop
   */
  async getActiveBundles(): Promise<BundleConfig[]> {
    const allBundles: BundleConfig[] = [];
    let hasNextPage = true;
    let after: string | undefined;
    
    // Fetch all pages
    while (hasNextPage) {
      const { bundles, pageInfo } = await this.getBundlesPaginated({ 
        first: 250, 
        after,
        activeOnly: true 
      });
      allBundles.push(...bundles);
      hasNextPage = pageInfo.hasNextPage;
      after = pageInfo.endCursor || undefined;
    }
    
    return allBundles;
  }

  /**
   * Get bundles for the shop with pagination support and caching
   */
  async getBundlesPaginated(options: { 
    first?: number; 
    after?: string;
    activeOnly?: boolean;
  } = {}): Promise<{ 
    bundles: BundleConfig[]; 
    pageInfo: { hasNextPage: boolean; endCursor: string | null };
  }> {
    const { first = 50, after, activeOnly = false } = options;
    
    // Create cache key
    const cacheKey = `bundles:${this.shop}:${first}:${after || 'start'}:${activeOnly}`;
    
    return cache.withCache(
      cacheKey,
      async () => {
        const query = `
          query getShopMetafields($namespace: String!, $first: Int!, $after: String) {
            shop {
              metafields(namespace: $namespace, first: $first, after: $after) {
                edges {
                  node {
                    key
                    value
                  }
                  cursor
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
          }
        `;

        const variables = {
          namespace: BUNDLE_NAMESPACE,
          first,
          after,
        };

        const response = await this.shopifyClient.request(query, { variables });
        
        const metafields = response.data?.shop?.metafields?.edges || [];
        const pageInfo = response.data?.shop?.metafields?.pageInfo || { 
          hasNextPage: false, 
          endCursor: null 
        };

        const bundles: BundleConfig[] = [];

        for (const edge of metafields) {
          if (edge.node.key.startsWith(BUNDLE_KEY)) {
            try {
              const bundle = JSON.parse(edge.node.value);
              if (!activeOnly || bundle.active) {
                bundles.push(bundle);
              }
            } catch (error) {
              console.error('Failed to parse bundle configuration:', error);
            }
          }
        }

        // Sort by newest first
        bundles.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime());

        return { bundles, pageInfo };
      },
      {
        ttl: CACHE_TTL.SHORT, // Bundles might change frequently
        namespace: 'bundles'
      }
    );
  }

  /**
   * Get all bundles for the shop (active and inactive) - legacy method for backward compatibility
   */
  async getAllBundles(): Promise<BundleConfig[]> {
    const allBundles: BundleConfig[] = [];
    let hasNextPage = true;
    let after: string | undefined;
    
    // Fetch all pages
    while (hasNextPage) {
      const { bundles, pageInfo } = await this.getBundlesPaginated({ 
        first: 250, 
        after 
      });
      allBundles.push(...bundles);
      hasNextPage = pageInfo.hasNextPage;
      after = pageInfo.endCursor || undefined;
    }
    
    return allBundles;
  }
  
  /**
   * Legacy method for backward compatibility
   * Returns all bundles without GraphQL client requirement
   */
  async getAllBundlesLegacy(): Promise<BundleConfig[]> {
    // This method is used when we don't have a GraphQL client
    // It returns an empty array for now, but could be extended
    // to use REST API or cached data if needed
    return [];
  }

  /**
   * Sync active bundles to shop metafields for Shopify Functions to access
   * This ensures the cart transform and discount functions can read bundle configurations
   */
  async syncBundlesToShopMetafields(): Promise<void> {
    try {
      const activeBundles = await this.getActiveBundles();

      // Clear existing bundle metafields first
      await this.clearBundleMetafields();

      // Set new bundle metafields
      const metafields = activeBundles.map(bundle => ({
        namespace: BUNDLE_NAMESPACE,
        key: `${BUNDLE_KEY}_${bundle.id}`,
        value: JSON.stringify(bundle),
        type: "json",
        ownerId: `gid://shopify/Shop/${this.shop}`,
      }));

      if (metafields.length > 0) {
        const mutation = `
          mutation metafieldSet($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              metafields {
                id
                namespace
                key
                value
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const response = await this.shopifyClient.request(mutation, { variables: { metafields } });

        if (response.data?.metafieldsSet?.userErrors?.length > 0) {
          throw new Error(`Failed to sync bundles: ${response.data.metafieldsSet.userErrors[0].message}`);
        }
      }
    } catch (error) {
      console.error('Failed to sync bundles to shop metafields:', error);
      throw error;
    }
  }

  /**
   * Clear existing bundle metafields from the shop
   */
  private async clearBundleMetafields(): Promise<void> {
    const query = `
      query getShopMetafields($namespace: String!) {
        shop {
          metafields(namespace: $namespace, first: 250) {
            edges {
              node {
                id
                key
              }
            }
          }
        }
      }
    `;

    const response = await this.shopifyClient.request(query, {
      variables: { namespace: BUNDLE_NAMESPACE }
    });

    const metafields = response.data?.shop?.metafields?.edges || [];
    const bundleMetafields = metafields.filter((edge: any) =>
      edge.node.key.startsWith(BUNDLE_KEY)
    );

    if (bundleMetafields.length > 0) {
      const deletePromises = bundleMetafields.map(async (edge: any) => {
        const mutation = `
          mutation metafieldDelete($input: MetafieldDeleteInput!) {
            metafieldDelete(input: $input) {
              deletedId
              userErrors {
                field
                message
              }
            }
          }
        `;

        return this.shopifyClient.request(mutation, {
          variables: {
            input: { id: edge.node.id }
          }
        });
      });

      await Promise.all(deletePromises);
    }
  }
}