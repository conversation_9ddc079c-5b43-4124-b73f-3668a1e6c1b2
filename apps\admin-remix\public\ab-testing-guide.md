# BundleForge A/B Testing Implementation Guide

This guide explains how to implement A/B testing for your bundles in your Shopify theme.

## 1. Include the Tracking Script

Add the BundleForge tracking script to your theme's `theme.liquid` file, just before the closing `</head>` tag:

```liquid
<script src="{{ 'bundleforge-tracker.js' | asset_url }}" defer></script>
```

Or if using the app proxy:

```liquid
<script src="/apps/bundleforge/bundleforge-tracker.js" defer></script>
```

## 2. Mark Bundle Elements

Add data attributes to elements that display bundles:

```liquid
<!-- On product pages -->
<div data-bundleforge-id="{{ bundle_id }}">
  <!-- Bundle content -->
</div>

<!-- Add to cart buttons -->
<button 
  data-bundleforge-add-to-cart="true"
  data-bundleforge-id="{{ bundle_id }}"
  class="add-to-cart-button"
>
  Add Bundle to Cart
</button>
```

## 3. Pass Bundle Information in Cart

When adding bundle products to cart, include bundle metadata:

```javascript
// Example: Adding bundle to cart with properties
const formData = {
  items: [
    {
      id: variantId,
      quantity: quantity,
      properties: {
        _bundle_id: bundleId,
        _bundle_name: bundleName,
        _ab_test_id: testId,        // Optional: if you have test ID
        _ab_test_variant: variant    // Optional: if you have variant
      }
    }
  ]
};

fetch('/cart/add.js', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(formData)
});
```

## 4. React to A/B Test Variants

Listen for variant assignments and adjust your UI accordingly:

```javascript
// Listen for A/B test variant assignment
window.addEventListener('bundleforge:ab-test-assigned', function(event) {
  const { bundle_id, test_id, variant } = event.detail;
  
  // Update UI based on variant
  if (variant === 'variant_b') {
    // Show variant B discount/messaging
    document.querySelector('.bundle-discount').textContent = 'Special Offer: 20% Off!';
  } else {
    // Show control (variant A) discount/messaging
    document.querySelector('.bundle-discount').textContent = 'Bundle Discount: 15% Off';
  }
});

// Check if customer is already assigned to a variant
document.addEventListener('DOMContentLoaded', function() {
  const bundleId = 'your-bundle-id';
  const variant = window.BundleForgeTracker.getABTestVariant(bundleId);
  
  if (variant) {
    // Customer already assigned, update UI
    updateBundleUI(variant);
  }
});
```

## 5. Tracking Purchase Events

On the order confirmation page, ensure bundle purchases are tracked:

```liquid
<!-- In order confirmation template -->
{% if checkout.line_items %}
  <script>
    window.Shopify = window.Shopify || {};
    window.Shopify.checkout = {
      order_id: "{{ checkout.order_id }}",
      line_items: [
        {% for line_item in checkout.line_items %}
          {
            variant_id: "{{ line_item.variant_id }}",
            product_id: "{{ line_item.product_id }}",
            quantity: {{ line_item.quantity }},
            price: "{{ line_item.price | money_without_currency | remove: ',' }}",
            properties: {
              {% for property in line_item.properties %}
                "{{ property.first }}": "{{ property.last }}"{% unless forloop.last %},{% endunless %}
              {% endfor %}
            }
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ]
    };
  </script>
{% endif %}
```

## 6. Advanced: Custom Tracking

For custom tracking events:

```javascript
// Track custom events
window.BundleForgeTracker.trackEvent('bundle_view', bundleId, {
  custom_field: 'value',
  page_type: 'collection'
});

// Check if A/B test is active for a bundle
if (window.BundleForgeTracker.isABTestActive(bundleId)) {
  // Show A/B test indicator
  document.querySelector('.ab-test-badge').style.display = 'block';
}
```

## 7. Best Practices

1. **Consistent Experience**: Ensure customers see the same variant throughout their session
2. **Fast Loading**: The tracker script is lightweight and loads asynchronously
3. **Privacy**: The tracker respects customer privacy and only tracks necessary data
4. **Fallbacks**: Always provide fallback behavior if the tracker fails to load

## Example Implementation

Here's a complete example for a product page:

```liquid
<!-- Product page bundle section -->
<div class="bundle-section" data-bundleforge-id="{{ bundle.id }}">
  <h3>{{ bundle.name }}</h3>
  
  <div class="bundle-discount">
    <!-- Discount will be updated based on A/B test variant -->
    <span class="discount-percentage">Loading...</span>
  </div>
  
  <div class="bundle-products">
    {% for product in bundle.products %}
      <div class="bundle-product">
        {{ product.title }} x {{ product.quantity }}
      </div>
    {% endfor %}
  </div>
  
  <button 
    class="bundle-add-to-cart"
    data-bundleforge-add-to-cart="true"
    data-bundleforge-id="{{ bundle.id }}"
  >
    Add Bundle to Cart
  </button>
</div>

<script>
  // Update bundle display based on A/B test variant
  document.addEventListener('DOMContentLoaded', function() {
    const bundleId = '{{ bundle.id }}';
    const bundleSection = document.querySelector(`[data-bundleforge-id="${bundleId}"]`);
    
    function updateBundleDisplay(variant) {
      const discountElement = bundleSection.querySelector('.discount-percentage');
      
      if (variant === 'variant_b') {
        discountElement.textContent = '20% Bundle Discount!';
        bundleSection.classList.add('variant-b');
      } else {
        discountElement.textContent = '15% Bundle Discount';
        bundleSection.classList.add('variant-a');
      }
    }
    
    // Check for existing variant assignment
    const existingVariant = window.BundleForgeTracker?.getABTestVariant(bundleId);
    if (existingVariant) {
      updateBundleDisplay(existingVariant);
    }
    
    // Listen for new assignments
    window.addEventListener('bundleforge:ab-test-assigned', function(event) {
      if (event.detail.bundle_id === bundleId) {
        updateBundleDisplay(event.detail.variant);
      }
    });
  });
</script>
```

## Support

For questions or issues with A/B testing implementation, please contact BundleForge support or refer to the admin dashboard for test performance metrics.