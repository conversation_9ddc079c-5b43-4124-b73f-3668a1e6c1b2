import { createCipheriv, createDecipheriv, randomBytes, scryptSync } from 'crypto';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const SALT_LENGTH = 32;
const TAG_LENGTH = 16;
const IV_LENGTH = 16;
const KEY_LENGTH = 32;

/**
 * Derive encryption key from master key
 */
function deriveKey(masterKey: string, salt: Buffer): Buffer {
  return scryptSync(masterKey, salt, KEY_LENGTH);
}

/**
 * Encrypt a value
 */
export function encryptValue(value: string, masterKey: string): string {
  const salt = randomBytes(SALT_LENGTH);
  const iv = randomBytes(IV_LENGTH);
  const key = deriveKey(masterKey, salt);
  
  const cipher = createCipheriv(ALGORITHM, key, iv);
  
  const encrypted = Buffer.concat([
    cipher.update(value, 'utf8'),
    cipher.final(),
  ]);
  
  const tag = cipher.getAuthTag();
  
  // Combine salt, iv, tag, and encrypted data
  const combined = Buffer.concat([salt, iv, tag, encrypted]);
  
  return combined.toString('base64');
}

/**
 * Decrypt a value
 */
export function decryptValue(encryptedValue: string, masterKey: string): string {
  const combined = Buffer.from(encryptedValue, 'base64');
  
  // Extract components
  const salt = combined.slice(0, SALT_LENGTH);
  const iv = combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
  const tag = combined.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
  const encrypted = combined.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
  
  const key = deriveKey(masterKey, salt);
  
  const decipher = createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(tag);
  
  const decrypted = Buffer.concat([
    decipher.update(encrypted),
    decipher.final(),
  ]);
  
  return decrypted.toString('utf8');
}

/**
 * Get master key from environment or generate one
 */
function getMasterKey(): string {
  // In production, this should come from a secure key management service
  // For now, we'll use an environment variable
  let masterKey = process.env.ENCRYPTION_MASTER_KEY;
  
  if (!masterKey) {
    // Generate a key for development (NOT FOR PRODUCTION)
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️  Using generated encryption key. Set ENCRYPTION_MASTER_KEY in production!');
      masterKey = randomBytes(32).toString('hex');
    } else {
      throw new Error('ENCRYPTION_MASTER_KEY must be set in production');
    }
  }
  
  return masterKey;
}

/**
 * Encrypted environment variable access
 */
export class SecureEnv {
  private static instance: SecureEnv;
  private masterKey: string;
  private cache: Map<string, string> = new Map();
  
  private constructor() {
    this.masterKey = getMasterKey();
  }
  
  static getInstance(): SecureEnv {
    if (!SecureEnv.instance) {
      SecureEnv.instance = new SecureEnv();
    }
    return SecureEnv.instance;
  }
  
  /**
   * Get an encrypted environment variable
   */
  get(key: string, encrypted: boolean = false): string | undefined {
    // Check cache first
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    const value = process.env[key];
    if (!value) {
      return undefined;
    }
    
    // If the value is encrypted, decrypt it
    if (encrypted && value.startsWith('ENC:')) {
      try {
        const encryptedPart = value.substring(4); // Remove 'ENC:' prefix
        const decrypted = decryptValue(encryptedPart, this.masterKey);
        this.cache.set(key, decrypted);
        return decrypted;
      } catch (error) {
        console.error(`Failed to decrypt ${key}`);
        throw new Error(`Invalid encrypted value for ${key}`);
      }
    }
    
    // Return plain value
    this.cache.set(key, value);
    return value;
  }
  
  /**
   * Get required encrypted environment variable
   */
  require(key: string, encrypted: boolean = false): string {
    const value = this.get(key, encrypted);
    if (!value) {
      throw new Error(`Environment variable ${key} is required`);
    }
    return value;
  }
  
  /**
   * Clear the cache (useful for testing)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

/**
 * Utility to encrypt environment variables for storage
 */
export function encryptForEnv(value: string): string {
  const masterKey = getMasterKey();
  const encrypted = encryptValue(value, masterKey);
  return `ENC:${encrypted}`;
}

/**
 * Export singleton instance
 */
export const secureEnv = SecureEnv.getInstance();

/**
 * Updated environment variable access with encryption support
 */
export const env = {
  // Shopify configuration (encrypted in production)
  get SHOPIFY_API_KEY() {
    return secureEnv.require('SHOPIFY_API_KEY', process.env.NODE_ENV === 'production');
  },
  
  get SHOPIFY_API_SECRET() {
    return secureEnv.require('SHOPIFY_API_SECRET', process.env.NODE_ENV === 'production');
  },
  
  get SHOPIFY_APP_URL() {
    return secureEnv.get('SHOPIFY_APP_URL');
  },
  
  get SHOPIFY_SCOPES() {
    return secureEnv.get('SHOPIFY_SCOPES') || 'read_products,write_products,read_orders,write_orders,read_discounts,write_discounts,read_metafields,write_metafields,read_cart_transforms,write_cart_transforms';
  },
  
  // Redis configuration
  get REDIS_URL() {
    return secureEnv.get('REDIS_URL', process.env.NODE_ENV === 'production') || 'redis://localhost:6379';
  },
  
  // Other configuration
  get NODE_ENV() {
    return process.env.NODE_ENV || 'development';
  },
  
  get ENCRYPTION_MASTER_KEY() {
    return process.env.ENCRYPTION_MASTER_KEY;
  },
};