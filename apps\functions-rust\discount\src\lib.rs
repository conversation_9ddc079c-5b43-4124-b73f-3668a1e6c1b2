use shopify_function::prelude::*;
use shopify_function::Result;
use bundleforge_shared::{BundleConfig, BundleMatch, parse_metafields, match_bundles, match_bundles_with_customer};
use std::collections::HashSet;

generate_types!(
    query_path = "./input.graphql",
    schema_path = "./schema.graphql"
);

// Implement trait wrappers for generated types
impl bundleforge_shared::MetafieldConnection for input::MetafieldConnection {
    type Edge = input::MetafieldEdge;
    
    fn edges(&self) -> Option<&Vec<Self::Edge>> {
        self.edges.as_ref()
    }
}

impl bundleforge_shared::MetafieldEdge for input::MetafieldEdge {
    type Node = input::Metafield;
    
    fn node(&self) -> Option<&Self::Node> {
        self.node.as_ref()
    }
}

impl bundleforge_shared::MetafieldNode for input::Metafield {
    fn namespace(&self) -> &str {
        &self.namespace
    }
    
    fn key(&self) -> &str {
        &self.key
    }
    
    fn value(&self) -> Option<&str> {
        self.value.as_deref()
    }
}

impl bundleforge_shared::Cart for input::Cart {
    type Line = input::CartLine;
    
    fn lines(&self) -> Option<&Vec<Self::Line>> {
        self.lines.as_ref()
    }
}

impl bundleforge_shared::CartLine for input::CartLine {
    type Merchandise = input::ProductVariant;
    
    fn id(&self) -> &str {
        &self.id
    }
    
    fn quantity(&self) -> i64 {
        self.quantity
    }
    
    fn merchandise(&self) -> Option<&Self::Merchandise> {
        match &self.merchandise {
            input::Merchandise::ProductVariant(variant) => Some(variant),
        }
    }
}

impl bundleforge_shared::Merchandise for input::ProductVariant {
    fn product_id(&self) -> &str {
        &self.product.id
    }
    
    fn variant_id(&self) -> &str {
        &self.id
    }
}

#[shopify_function_target]
fn function(input: input::ResponseData) -> Result<output::FunctionResult> {
    // Add panic handler for production safety
    std::panic::set_hook(Box::new(|_| {
        eprintln!("Discount function panicked, returning no-op");
    }));
    
    let result = match process_discounts(&input.cart) {
        Ok(discounts) => output::FunctionResult {
            discounts,
            discount_application_strategy: output::DiscountApplicationStrategy::First,
        },
        Err(e) => {
            eprintln!("Error processing discounts: {}", e);
            output::FunctionResult {
                discounts: vec![],
                discount_application_strategy: output::DiscountApplicationStrategy::First,
            }
        }
    };
    
    Ok(result)
}

fn process_discounts(cart: &input::Cart) -> Result<Vec<output::Discount>> {
    // Extract bundle configurations from cart metafields
    let bundle_configs = if let Some(metafields) = &cart.metafields {
        parse_metafields(metafields)
    } else {
        vec![]
    };
    
    if bundle_configs.is_empty() {
        return Ok(vec![]);
    }
    
    // Analyze cart for complete bundles with A/B test support
    let bundle_matches = if let Some(metafields) = &cart.metafields {
        // Use customer-aware matching if we have metafields (which may contain customer ID)
        match_bundles_with_customer::<_, input::CartLine, _>(cart, &bundle_configs, metafields)
    } else {
        // Fall back to regular matching
        match_bundles(cart, &bundle_configs)
    };
    
    // Generate discount operations for complete bundles
    let discounts = generate_discount_operations(&bundle_matches);
    
    // Shopify limits to 25 active discounts
    if discounts.len() > 25 {
        eprintln!("Too many discounts ({}) - limiting to 25", discounts.len());
        return Ok(discounts.into_iter().take(25).collect());
    }
    
    Ok(discounts)
}

fn generate_discount_operations(bundle_matches: &[BundleMatch]) -> Vec<output::Discount> {
    let mut discounts = Vec::new();
    let mut used_lines = HashSet::new();
    
    for bundle_match in bundle_matches {
        if bundle_match.is_complete && bundle_match.quantity_multiplier > 0 {
            // Create targets for this bundle's matched lines
            let mut targets = Vec::new();
            
            for matched_line in &bundle_match.matched_lines {
                // Skip lines already used in other bundles to prevent double discounting
                if used_lines.contains(&matched_line.line_id) {
                    continue;
                }
                
                targets.push(output::Target {
                    product_variant: Some(output::ProductVariantTarget {
                        id: matched_line.variant_id.clone(),
                        quantity: Some(matched_line.quantity_used as i32),
                    }),
                });
                
                used_lines.insert(matched_line.line_id.clone());
            }
            
            if !targets.is_empty() {
                // Create a percentage discount for the bundle
                let discount_message = format!(
                    "{}% off {} bundle{}",
                    bundle_match.discount_percentage,
                    bundle_match.bundle_name,
                    if bundle_match.quantity_multiplier > 1 { 
                        format!(" (x{})", bundle_match.quantity_multiplier) 
                    } else { 
                        String::new() 
                    }
                );
                
                discounts.push(output::Discount {
                    message: Some(discount_message),
                    conditions: Some(vec![]),
                    targets,
                    value: output::Value {
                        percentage: Some(output::Percentage {
                            value: format!("{:.2}", bundle_match.discount_percentage),
                        }),
                        fixed_amount: None,
                    },
                });
            }
        }
    }
    
    discounts
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_empty_cart() {
        // Test implementation
    }
    
    #[test]
    fn test_bundle_discount_application() {
        // Test implementation
    }
    
    #[test]
    fn test_multiple_bundles() {
        // Test implementation
    }
}