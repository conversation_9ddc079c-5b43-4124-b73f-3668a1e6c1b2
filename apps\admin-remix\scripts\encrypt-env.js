#!/usr/bin/env node

/**
 * Script to encrypt environment variables for secure storage
 * Usage: node scripts/encrypt-env.js <variable_name> <value>
 * Example: node scripts/encrypt-env.js SHOPIFY_API_SECRET "my-secret-key"
 */

const { encryptForEnv } = require('../app/libs/env-encryption.server');

function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.error('Usage: node scripts/encrypt-env.js <variable_name> <value>');
    console.error('Example: node scripts/encrypt-env.js SHOPIFY_API_SECRET "my-secret-key"');
    process.exit(1);
  }
  
  const [varName, value] = args;
  
  try {
    // Set a temporary master key if not set
    if (!process.env.ENCRYPTION_MASTER_KEY) {
      console.warn('⚠️  ENCRYPTION_MASTER_KEY not set. Using a temporary key.');
      console.warn('⚠️  Set ENCRYPTION_MASTER_KEY environment variable before encrypting production values!');
      process.env.ENCRYPTION_MASTER_KEY = require('crypto').randomBytes(32).toString('hex');
      console.warn(`⚠️  Temporary key: ${process.env.ENCRYPTION_MASTER_KEY}`);
    }
    
    const encrypted = encryptForEnv(value);
    
    console.log('\n✅ Successfully encrypted value!');
    console.log('\nAdd this to your .env file:');
    console.log(`${varName}=${encrypted}`);
    console.log('\n⚠️  Remember to keep your ENCRYPTION_MASTER_KEY secure!');
    
  } catch (error) {
    console.error('❌ Error encrypting value:', error.message);
    process.exit(1);
  }
}

main();