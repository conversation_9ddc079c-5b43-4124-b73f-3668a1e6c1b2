# BundleForge - Shopify App

A **production-ready Shopify App** for creating and managing product bundles with comprehensive analytics, monitoring, and caching capabilities, fully integrated with Shopify's ecosystem.

## ✅ Shopify App Compliance

This app is properly configured as a Shopify App with:

- **Proper App Configuration**: `shopify.app.toml` with correct scopes and webhooks
- **App Bridge Integration**: Embedded UI that works within Shopify Admin
- **Metafield-Based Storage**: Uses Shopify's native metafields instead of external databases
- **Shopify Functions Integration**: Cart transform and discount functions that read from metafields
- **Standard Authentication**: Uses Shopify's OAuth flow and session management
- **GDPR Compliance**: Built-in webhooks for data privacy compliance

## 🚀 Features

### Core Bundle Management
- **Bundle Management**: Create, edit, and manage product bundles through an intuitive admin interface
- **Cart Transformation**: Automatically suggest bundle completions when customers add related products
- **Discount Application**: Apply bundle discounts when customers have all required products in cart
- **Real-time Sync**: Bundle configurations automatically sync to Shopify Functions
- **Embedded Admin UI**: Fully integrated with Shopify Admin using App Bridge
- **Native Data Storage**: All data stored in Shopify metafields (no external database needed)

### Production-Ready Features
- **Analytics Dashboard**: Comprehensive analytics with conversion tracking, A/B testing support, and performance metrics
- **Real-time Monitoring**: System health monitoring with circuit breakers, error tracking, and performance metrics
- **Advanced Caching**: Redis-based caching with in-memory fallback for optimal performance
- **Error Handling**: Production-grade error handling with structured logging and correlation IDs
- **Security**: Rate limiting, CSRF protection, security headers, and webhook verification
- **Testing Suite**: Comprehensive test coverage with Vitest and testing utilities

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin App     │    │     Redis       │    │ Shopify         │
│   (Remix)       │◄──►│    Cache        │    │ Functions       │
│   App Bridge    │    │   + Fallback    │    │ (Rust)          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Analytics &    │    │  Monitoring &   │    │ Shopify         │
│  Error Tracking │    │  Health Checks  │    │ Store           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │  Shopify        │
                    │  Metafields     │
                    │  (Data Storage) │
                    └─────────────────┘
```

**Key Components:**
- **Admin App** (`apps/admin-remix`): Production-ready Remix app with comprehensive features
- **Shopify Functions** (`apps/functions-rust`): Rust-based cart transform and discount functions
- **Caching Layer**: Redis with in-memory fallback for optimal performance
- **Analytics System**: Conversion tracking, A/B testing, and performance metrics
- **Monitoring**: Real-time health checks, error tracking, and system metrics
- **Metafields**: Native Shopify data storage for bundle configurations

## 🚀 How It Works (SaaS Model)

BundleForge is a **SaaS Shopify App** that merchants install from the Shopify App Store. Here's how it works:

### For Merchants (Your Customers)
1. **Install from App Store**: Merchants find and install BundleForge from the Shopify App Store
2. **Automatic Setup**: The app automatically configures itself for their store
3. **Create Bundles**: Merchants use the embedded interface to create product bundles
4. **Analytics & Monitoring**: Built-in analytics track bundle performance
5. **No Technical Setup**: Merchants don't need any technical configuration

### For You (App Developer)
1. **Single Deployment**: Deploy once to serve all merchants
2. **Multi-Tenant**: Each merchant's data is isolated and secure
3. **Automatic Scaling**: Handles multiple stores simultaneously
4. **Revenue Model**: Charge merchants via Shopify's billing API

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- Shopify Partner Account
- Shopify CLI 3.x
- Redis (optional, for production caching)

### 1. Clone and Install

```bash
git clone <repository-url>
cd BundleForge
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

**For Development** (these are filled automatically by Shopify CLI):
```env
# Shopify Configuration (auto-filled by `shopify app dev`)
SHOPIFY_API_KEY=
SHOPIFY_API_SECRET=

# Session Configuration (auto-generated in production)
SESSION_SECRET=your_dev_session_secret

# Optional: Redis for caching (uses in-memory fallback if not provided)
REDIS_URL=redis://localhost:6379
```

### 3. Start Development

```bash
# This handles everything - tunneling, OAuth, webhooks
shopify app dev
```

The Shopify CLI will:
- Create a secure tunnel to your local app
- Set up OAuth callbacks automatically
- Configure webhooks
- Open your development store

### 4. Production Deployment

```bash
# Deploy to your hosting platform (Fly.io, Railway, etc.)
shopify app deploy
```

The app automatically:
- Detects its production URL
- Handles OAuth for any merchant who installs it
- Manages sessions for multiple stores
- Isolates data per merchant

## 💼 For Merchants (Your Customers)

### Installing BundleForge
1. **Find in App Store**: Search for "BundleForge" in the Shopify App Store
2. **One-Click Install**: Click "Install" - no technical setup required
3. **Automatic Configuration**: The app configures itself for your store
4. **Start Creating Bundles**: Access BundleForge from your Shopify Admin

### Using BundleForge

#### Creating a Bundle
1. Open BundleForge from your Shopify Admin sidebar
2. Click "Create Bundle" from the dashboard
3. Enter bundle name and discount percentage
4. Add products to the bundle with quantities
5. Save the bundle

The system automatically:
- Stores bundle configuration securely
- Enables discount application on your storefront
- Starts tracking performance analytics

#### Dashboard Features
- **Bundle Overview**: See all your bundles and their performance
- **Analytics**: Track conversion rates, revenue, and customer behavior
- **Monitoring**: Real-time health status of your bundles
- **Settings**: Customize bundle behavior and preferences

#### Analytics & Insights
- Bundle performance metrics and conversion funnels
- Revenue attribution from bundle sales
- A/B testing capabilities for optimization
- Customer behavior tracking

### How Bundles Work on Your Store
1. **Automatic Detection**: System detects when customers add bundle products
2. **Smart Suggestions**: Suggests completing bundles at optimal moments
3. **Instant Discounts**: Applies bundle discounts automatically at checkout
4. **Performance Tracking**: Monitors and reports on bundle effectiveness

## 🔧 For Developers (SaaS Architecture)

### Multi-Tenant Design
- **Shop Isolation**: Each merchant's data is completely isolated
- **Session Management**: Secure session handling for multiple stores
- **Webhook Verification**: HMAC verification ensures data integrity
- **Metafield Namespacing**: Each shop's bundles stored in their own metafields

### Automatic Scaling
- **Dynamic OAuth**: Handles installation for unlimited merchants
- **Session Storage**: Redis-based sessions with in-memory fallback
- **Caching Strategy**: Per-shop caching with automatic invalidation
- **Error Isolation**: Issues in one shop don't affect others

## 💰 SaaS Business Model

### Revenue Streams
- **Subscription Plans**: Monthly/yearly recurring revenue from merchants
- **Usage-Based Pricing**: Charge based on bundle performance or transactions
- **Premium Features**: Advanced analytics, A/B testing, custom integrations
- **Enterprise Plans**: White-label solutions for larger merchants

### Shopify Billing Integration
The app is ready for Shopify's billing API to handle:
- Automatic subscription management
- Trial periods and plan upgrades
- Revenue sharing with Shopify
- Compliance with App Store policies

### Merchant Value Proposition
- **Increase AOV**: Bundle products to increase average order value
- **Reduce Cart Abandonment**: Smart bundle suggestions at optimal moments
- **Data-Driven Insights**: Analytics to optimize bundle performance
- **No Technical Setup**: Works immediately after installation

## 📁 Project Structure

```
BundleForge/
├── shopify.app.toml          # Main Shopify app configuration
├── apps/
│   ├── admin-remix/          # Production-ready admin interface
│   │   ├── shopify.web.toml  # Web app configuration
│   │   ├── app/
│   │   │   ├── routes/       # All app routes and API endpoints
│   │   │   │   ├── _index.tsx           # Dashboard
│   │   │   │   ├── bundles/             # Bundle management
│   │   │   │   ├── app.analytics.tsx    # Analytics dashboard
│   │   │   │   ├── app.monitoring.tsx   # System monitoring
│   │   │   │   ├── app.preferences.tsx  # Settings
│   │   │   │   ├── api.analytics.tsx    # Analytics API
│   │   │   │   ├── api.health.tsx       # Health check API
│   │   │   │   └── webhooks/            # Webhook handlers
│   │   │   └── libs/         # Core services and utilities
│   │   │       ├── auth.server.ts       # Authentication
│   │   │       ├── bundle.server.ts     # Bundle management
│   │   │       ├── metafield.server.ts  # Metafield operations
│   │   │       ├── cache.server.ts      # Caching layer
│   │   │       ├── error-handler.server.ts # Error handling
│   │   │       ├── validation.server.ts # Data validation
│   │   │       └── security-*.server.ts # Security features
│   │   ├── test/             # Comprehensive test suite
│   │   └── scripts/          # Deployment scripts
│   └── functions-rust/       # Shopify Functions (optional)
│       ├── cart_transform/   # Bundle suggestion logic
│       └── discount/         # Bundle discount application
```

## 🔧 **Development Commands**

```bash
# Start development server
npm run dev                    # Starts Shopify app with hot reload
npm run dev:admin             # Start only admin app

# Install dependencies
npm install                    # Root dependencies
cd apps/admin-remix && npm install  # Admin app dependencies

# Testing
npm run test                   # Run all tests
cd apps/admin-remix && npm run test:coverage  # Run tests with coverage

# Build and deploy
npm run build                  # Build all packages
npm run deploy                 # Deploy to Shopify

# Functions development (optional)
cd apps/functions-rust
cargo build                    # Build Rust functions
npm run deploy:functions      # Deploy functions to Shopify

# Utilities
npm run lint                   # Lint all code
npm run format                 # Format code with Prettier
npm run clean                  # Clean build artifacts
```

## 🧪 **Testing & Quality Assurance**

### Testing Bundle Functionality

1. **Create a Bundle**:
   - Navigate to BundleForge in Shopify Admin
   - Create a new bundle with 2+ products
   - Set a discount percentage and save

2. **Test Analytics**:
   - View the analytics dashboard
   - Check conversion metrics and performance data
   - Verify A/B testing placeholders

3. **Test Monitoring**:
   - Access the monitoring dashboard
   - Check system health and error tracking
   - Verify circuit breaker status

4. **Test Cart Transform** (if using Shopify Functions):
   - Add bundle products to cart on storefront
   - Verify bundle suggestions appear

5. **Test Discount Application** (if using Shopify Functions):
   - Complete bundle in cart
   - Verify discount is applied at checkout

### Running Tests

```bash
# Run all tests
cd apps/admin-remix
npm run test

# Run tests with coverage
npm run test:coverage

# Run specific test files
npm run test -- metafield.server.test.ts
```

### Health Checks

The app includes comprehensive health monitoring:
- **API Health**: `/api/health` endpoint
- **System Metrics**: Memory usage, uptime, response times
- **Circuit Breaker Status**: External service health
- **Error Tracking**: Structured logging with correlation IDs

## 🚀 **Production Features**

### ✅ **Core Functionality**
- **Bundle Management**: Complete CRUD operations for product bundles
- **Metafield Integration**: Custom metafield service with caching
- **Shopify Functions**: Cart transform and discount application (Rust-based)
- **Authentication**: Secure OAuth flow with session management
- **GDPR Compliance**: Built-in webhooks for data privacy

### ✅ **Production-Ready Features**
- **Analytics System**: Conversion tracking, performance metrics, A/B testing support
- **Monitoring Dashboard**: Real-time health checks, error tracking, system metrics
- **Advanced Caching**: Redis with in-memory fallback, cache invalidation strategies
- **Error Handling**: Structured logging, correlation IDs, circuit breaker patterns
- **Security**: Rate limiting, CSRF protection, security headers, webhook verification
- **Testing**: Comprehensive test suite with Vitest, mocking, and coverage reports

### ✅ **Deployment Ready**
- **Automated Session Secrets**: PowerShell and bash scripts for secure deployment
- **Environment Configuration**: Flexible config with sensible defaults
- **Health Monitoring**: `/api/health` endpoint for load balancer checks
- **Performance Optimization**: Caching, error recovery, and resilience patterns

## 📊 **Architecture Evolution**

| **Feature** | **Implementation** | **Status** |
|-------------|-------------------|------------|
| Bundle Management | Metafield-based with caching | ✅ Complete |
| Analytics | Conversion tracking + dashboard | ✅ Complete |
| Monitoring | Health checks + error tracking | ✅ Complete |
| Caching | Redis + in-memory fallback | ✅ Complete |
| Security | Rate limiting + CSRF + headers | ✅ Complete |
| Testing | Vitest + comprehensive coverage | ✅ Complete |
| Error Handling | Structured logging + circuit breakers | ✅ Complete |
| Deployment | Automated scripts + health checks | ✅ Complete |

The app is **production-ready** with enterprise-grade features and follows Shopify's best practices!

## 🔌 **API Endpoints**

### Admin Routes
- `/` - Dashboard with bundle overview and quick actions
- `/bundles` - Bundle management interface
- `/bundles/new` - Create new bundle
- `/bundles/:id` - View/edit specific bundle
- `/app/analytics` - Analytics dashboard with conversion metrics
- `/app/monitoring` - System monitoring and health status
- `/app/preferences` - App settings and configuration

### API Endpoints
- `/api/health` - Health check endpoint for monitoring
- `/api/analytics` - Analytics data API with filtering

### Webhook Endpoints
- `/webhooks/app/uninstalled` - App lifecycle management
- `/webhooks/gdpr` - GDPR compliance (data requests, redaction)
- `/webhooks/orders` - Order tracking for analytics
- `/webhooks/products` - Product sync for bundle management

## 🛡️ **Security Features**

- **Authentication**: Shopify OAuth with secure session management
- **CSRF Protection**: Built-in CSRF token validation
- **Rate Limiting**: Configurable request rate limiting
- **Security Headers**: Comprehensive security header implementation
- **Webhook Verification**: HMAC verification for all webhooks
- **Input Validation**: Zod schema validation for all data
- **Error Masking**: Production-safe error responses

## 📈 **Performance Features**

- **Caching**: Multi-layer caching with Redis and in-memory fallback
- **Circuit Breakers**: Automatic failure detection and recovery
- **Connection Pooling**: Optimized database connections
- **Lazy Loading**: Efficient resource loading strategies
- **Correlation IDs**: Request tracking for debugging and monitoring
