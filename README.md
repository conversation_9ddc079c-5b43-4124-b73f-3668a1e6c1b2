# BundleForge - Shopify App

A **proper Shopify App** for creating and managing product bundles with automatic discount application, fully integrated with Shopify's ecosystem.

## ✅ Shopify App Compliance

This app is now properly configured as a Shopify App with:

- **Proper App Configuration**: `shopify.app.toml` with correct scopes and webhooks
- **App Bridge Integration**: Embedded UI that works within Shopify Admin
- **Metafield-Based Storage**: Uses Shopify's native metafields instead of external databases
- **Shopify Functions Integration**: Cart transform and discount functions that read from metafields
- **Standard Authentication**: Uses Shopify's OAuth flow and session management
- **GDPR Compliance**: Built-in webhooks for data privacy compliance

## 🚀 Features

- **Bundle Management**: Create, edit, and manage product bundles through an intuitive admin interface
- **Cart Transformation**: Automatically suggest bundle completions when customers add related products
- **Discount Application**: Apply bundle discounts when customers have all required products in cart
- **Real-time Sync**: Bundle configurations automatically sync to Shopify Functions
- **Embedded Admin UI**: Fully integrated with Shopify Admin using App Bridge
- **Native Data Storage**: All data stored in Shopify metafields (no external database needed)

## 🏗️ Architecture

```
┌─────────────────┐                    ┌─────────────────┐
│   Admin App     │                    │ Shopify         │
│   (Remix)       │◄──────────────────►│ Functions       │
│   App Bridge    │                    │ (Rust)          │
└─────────────────┘                    └─────────────────┘
         │                                       │
         ▼                                       ▼
┌─────────────────┐                    ┌─────────────────┐
│  Shopify        │                    │ Shopify         │
│  Metafields     │◄──────────────────►│ Store           │
│  (Data Storage) │                    │ (Frontend)      │
└─────────────────┘                    └─────────────────┘
```

**Key Components:**
- **Admin App** (`apps/admin-remix`): Remix-based admin interface embedded in Shopify Admin
- **Shopify Functions** (`apps/functions-rust`): Rust-based cart transform and discount functions
- **Metafields**: Native Shopify data storage for bundle configurations
- **App Bridge**: Seamless integration with Shopify Admin UI

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Rust (for Shopify Functions)
- Shopify Partner Account
- Shopify CLI 3.x
- ngrok (for local development)

### 1. Clone and Install

```bash
git clone <repository-url>
cd BundleForge
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Update `.env` with your configuration:

```env
# Shopify Configuration
# Shopify Configuration (SHOPIFY_API_KEY is your app's Client ID)
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_APP_URL=https://localhost:3000 # For local development, Shopify CLI handles tunneling

# Session Configuration
SESSION_SECRET=your_session_secret
```

### 3. Start Development

```bash
# Terminal 1: Start the Shopify app
shopify app dev

# Optional: Terminal 2: Start ngrok (if you prefer not to use Shopify CLI's built-in tunnel)
# ngrok http 3000
```

### 4. Install Dependencies

```bash
# Install admin app dependencies
cd apps/admin-remix
npm install

# Install Shopify Functions dependencies
cd ../functions-rust
cargo build
```

### 5. Deploy to Shopify

```bash
# Deploy the entire app including functions
shopify app deploy
```

### 6. Configure Shopify App

1. Create a new app in your Shopify Partner dashboard
2. Set the app URL to your production domain (e.g., `https://your-app.com`)
3. Set the Allowed redirection URL(s) to:
   ```
   https://your-app.com/auth/callback
   https://your-app.com/api/auth/callback
   ```
   For local development, these would be `https://localhost:3000/auth/callback` and `https://localhost:3000/api/auth/callback`.
4. Configure the required scopes:
   - `read_products`
   - `write_products`
   - `read_metafields`
   - `write_metafields`
   - `read_orders`
   - `write_orders`
   - `write_cart_transforms`
   - `write_discounts`

## Usage

### Creating a Bundle

1. Navigate to the admin app
2. Click "Create Bundle"
3. Enter bundle name and discount percentage
4. Add products to the bundle
5. Save the bundle

The system will:
- Create a ghost product in Shopify
- Store bundle configuration in metafields
- Enable automatic discount application

### How It Works

1. **Bundle Creation**: Admin creates bundle through the web interface
2. **Ghost Product**: System creates a hidden product representing the bundle
3. **Metafields Storage**: Bundle configuration stored in Shopify metafields
4. **Cart Analysis**: Shopify Function analyzes cart contents
5. **Discount Application**: Function applies bundle discounts automatically

## 📁 Project Structure

```
BundleForge/
├── shopify.app.toml          # Main Shopify app configuration
├── apps/
│   ├── admin-remix/          # Admin web interface (Remix + App Bridge)
│   │   ├── shopify.web.toml  # Web app configuration
│   │   └── app/libs/         # Bundle service using metafields
│   └── functions-rust/       # Shopify Functions (cart transform + discount)
│       ├── cart_transform/   # Bundle suggestion logic
│       └── discount/         # Bundle discount application
└── packages/
    └── shopify-metafields/   # Metafields utilities
```

## 🔧 **Development Commands**

```bash
# Start development server
npm run dev                    # Starts Shopify app with hot reload

# Install dependencies
npm install                    # Root dependencies
cd apps/admin-remix && npm install  # Admin app dependencies

# Build and deploy
npm run build                  # Build all packages
npm run deploy                 # Deploy to Shopify

# Functions development
cd apps/functions-rust
cargo build                    # Build Rust functions
shopify app deploy            # Deploy functions to Shopify
```

## 🧪 **Testing Bundle Functionality**

1. **Create a Bundle**:
   - Navigate to the admin app in Shopify Admin
   - Create a new bundle with 2+ products
   - Set a discount percentage

2. **Test Cart Transform**:
   - Add bundle products to cart on storefront
   - Verify bundle suggestions appear

3. **Test Discount Application**:
   - Complete bundle in cart
   - Verify discount is applied at checkout

## 🚀 **What Changed in the Cleanup**

### ✅ **Removed Unnecessary Files**
- **`services/ghost-sku-service/`** - No longer needed with metafield-based approach
- **`src/` (Next.js app)** - Wrong framework, this is a Remix app
- **`public/` (Next.js assets)** - Not needed for embedded Shopify app
- **`.docs/`** - Outdated documentation with old architecture
- **`next.config.ts`** - Next.js configuration not needed
- **`tsconfig.json` (root)** - Conflicted with Remix configuration
- **Config files** - Removed unused ESLint and PostCSS configs

### ✅ **Updated Configuration**
- **`package.json`** - Removed references to deleted services
- **`pnpm-workspace.yaml`** - Updated workspace paths
- **`README.md`** - Cleaned up outdated architecture documentation

### ✅ **Kept Essential Files**
- **`shopify.app.toml`** - Main app configuration
- **`apps/admin-remix/`** - Core admin interface
- **`apps/functions-rust/`** - Shopify Functions
- **`packages/shopify-metafields/`** - Utility package

## 📊 **Before vs After**

| **Before** | **After** |
|------------|-----------|
| 🔴 External Supabase database | ✅ Native Shopify metafields |
| 🔴 Separate ghost SKU service | ✅ Integrated metafield management |
| 🔴 Mixed Next.js + Remix files | ✅ Clean Remix-only structure |
| 🔴 Outdated documentation | ✅ Current architecture docs |
| 🔴 Complex multi-service setup | ✅ Simple Shopify App structure |

The app is now **significantly cleaner** and follows Shopify's best practices for app development!
