# BundleForge - Shopify App

A **production-ready Shopify App** for creating and managing product bundles with comprehensive analytics, monitoring, and caching capabilities, fully integrated with Shopify's ecosystem.

## ✅ Shopify App Compliance

This app is properly configured as a Shopify App with:

- **Proper App Configuration**: `shopify.app.toml` with correct scopes and webhooks
- **App Bridge Integration**: Embedded UI that works within Shopify Admin
- **Metafield-Based Storage**: Uses Shopify's native metafields instead of external databases
- **Shopify Functions Integration**: Cart transform and discount functions that read from metafields
- **Standard Authentication**: Uses Shopify's OAuth flow and session management
- **GDPR Compliance**: Built-in webhooks for data privacy compliance

## 🚀 Features

### Core Bundle Management
- **Bundle Management**: Create, edit, and manage product bundles through an intuitive admin interface
- **Cart Transformation**: Automatically suggest bundle completions when customers add related products
- **Discount Application**: Apply bundle discounts when customers have all required products in cart
- **Real-time Sync**: Bundle configurations automatically sync to Shopify Functions
- **Embedded Admin UI**: Fully integrated with Shopify Admin using App Bridge
- **Native Data Storage**: All data stored in Shopify metafields (no external database needed)

### Production-Ready Features
- **Analytics Dashboard**: Comprehensive analytics with conversion tracking, A/B testing support, and performance metrics
- **Real-time Monitoring**: System health monitoring with circuit breakers, error tracking, and performance metrics
- **Advanced Caching**: Redis-based caching with in-memory fallback for optimal performance
- **Error Handling**: Production-grade error handling with structured logging and correlation IDs
- **Security**: Rate limiting, CSRF protection, security headers, and webhook verification
- **Testing Suite**: Comprehensive test coverage with Vitest and testing utilities

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin App     │    │     Redis       │    │ Shopify         │
│   (Remix)       │◄──►│    Cache        │    │ Functions       │
│   App Bridge    │    │   + Fallback    │    │ (Rust)          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Analytics &    │    │  Monitoring &   │    │ Shopify         │
│  Error Tracking │    │  Health Checks  │    │ Store           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │  Shopify        │
                    │  Metafields     │
                    │  (Data Storage) │
                    └─────────────────┘
```

**Key Components:**
- **Admin App** (`apps/admin-remix`): Production-ready Remix app with comprehensive features
- **Shopify Functions** (`apps/functions-rust`): Rust-based cart transform and discount functions
- **Caching Layer**: Redis with in-memory fallback for optimal performance
- **Analytics System**: Conversion tracking, A/B testing, and performance metrics
- **Monitoring**: Real-time health checks, error tracking, and system metrics
- **Metafields**: Native Shopify data storage for bundle configurations

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Rust (for Shopify Functions)
- Shopify Partner Account
- Shopify CLI 3.x
- ngrok (for local development)

### 1. Clone and Install

```bash
git clone <repository-url>
cd BundleForge
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Update `.env` with your configuration:

```env
# Shopify Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_APP_URL=https://localhost:3000

# Session Configuration (auto-generated in production)
SESSION_SECRET=your_session_secret

# Optional: Redis for caching (uses in-memory fallback if not provided)
REDIS_URL=redis://localhost:6379

# Optional: Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000
```

### 3. Start Development

```bash
# Terminal 1: Start the Shopify app
shopify app dev

# Optional: Terminal 2: Start ngrok (if you prefer not to use Shopify CLI's built-in tunnel)
# ngrok http 3000
```

### 4. Install Dependencies

```bash
# Install admin app dependencies
cd apps/admin-remix
npm install

# Install Shopify Functions dependencies (if using functions)
cd ../functions-rust
cargo build
```

### 5. Deploy to Shopify

```bash
# Deploy the entire app including functions
shopify app deploy
```

### 6. Configure Shopify App

1. Create a new app in your Shopify Partner dashboard
2. Set the app URL to your production domain (e.g., `https://your-app.com`)
3. Set the Allowed redirection URL(s) to:
   ```
   https://your-app.com/auth/callback
   https://your-app.com/api/auth/callback
   ```
   For local development, these would be `https://localhost:3000/auth/callback` and `https://localhost:3000/api/auth/callback`.
4. Configure the required scopes:
   - `read_products`
   - `write_products`
   - `read_metafields`
   - `write_metafields`
   - `read_orders`
   - `write_orders`
   - `write_cart_transforms`
   - `write_discounts`

## Usage

### Creating a Bundle

1. Navigate to the BundleForge app in Shopify Admin
2. Click "Create Bundle" from the dashboard
3. Enter bundle name and discount percentage
4. Add products to the bundle with quantities
5. Save the bundle

The system will:
- Store bundle configuration in Shopify metafields
- Enable automatic discount application via Shopify Functions
- Start tracking analytics for the bundle

### Admin Features

#### Dashboard
- View total bundles and active bundles
- See recent bundle activity
- Quick access to analytics, settings, and monitoring

#### Analytics
- Bundle performance metrics
- Conversion funnel tracking (views → cart adds → purchases)
- A/B testing support
- Revenue attribution

#### Monitoring
- Real-time system health checks
- Error tracking with correlation IDs
- Performance metrics and uptime monitoring
- Circuit breaker status

#### Settings & Preferences
- App configuration options
- Bundle management settings
- Integration preferences

### How It Works

1. **Bundle Creation**: Admin creates bundle through the embedded interface
2. **Metafields Storage**: Bundle configuration stored in Shopify metafields with caching
3. **Cart Analysis**: Shopify Functions analyze cart contents for bundle opportunities
4. **Discount Application**: Functions apply bundle discounts automatically
5. **Analytics Tracking**: System tracks conversions and performance metrics
6. **Real-time Monitoring**: Health checks ensure system reliability

## 📁 Project Structure

```
BundleForge/
├── shopify.app.toml          # Main Shopify app configuration
├── apps/
│   ├── admin-remix/          # Production-ready admin interface
│   │   ├── shopify.web.toml  # Web app configuration
│   │   ├── app/
│   │   │   ├── routes/       # All app routes and API endpoints
│   │   │   │   ├── _index.tsx           # Dashboard
│   │   │   │   ├── bundles/             # Bundle management
│   │   │   │   ├── app.analytics.tsx    # Analytics dashboard
│   │   │   │   ├── app.monitoring.tsx   # System monitoring
│   │   │   │   ├── app.preferences.tsx  # Settings
│   │   │   │   ├── api.analytics.tsx    # Analytics API
│   │   │   │   ├── api.health.tsx       # Health check API
│   │   │   │   └── webhooks/            # Webhook handlers
│   │   │   └── libs/         # Core services and utilities
│   │   │       ├── auth.server.ts       # Authentication
│   │   │       ├── bundle.server.ts     # Bundle management
│   │   │       ├── metafield.server.ts  # Metafield operations
│   │   │       ├── cache.server.ts      # Caching layer
│   │   │       ├── error-handler.server.ts # Error handling
│   │   │       ├── validation.server.ts # Data validation
│   │   │       └── security-*.server.ts # Security features
│   │   ├── test/             # Comprehensive test suite
│   │   └── scripts/          # Deployment scripts
│   └── functions-rust/       # Shopify Functions (optional)
│       ├── cart_transform/   # Bundle suggestion logic
│       └── discount/         # Bundle discount application
```

## 🔧 **Development Commands**

```bash
# Start development server
npm run dev                    # Starts Shopify app with hot reload
npm run dev:admin             # Start only admin app

# Install dependencies
npm install                    # Root dependencies
cd apps/admin-remix && npm install  # Admin app dependencies

# Testing
npm run test                   # Run all tests
cd apps/admin-remix && npm run test:coverage  # Run tests with coverage

# Build and deploy
npm run build                  # Build all packages
npm run deploy                 # Deploy to Shopify

# Functions development (optional)
cd apps/functions-rust
cargo build                    # Build Rust functions
npm run deploy:functions      # Deploy functions to Shopify

# Utilities
npm run lint                   # Lint all code
npm run format                 # Format code with Prettier
npm run clean                  # Clean build artifacts
```

## 🧪 **Testing & Quality Assurance**

### Testing Bundle Functionality

1. **Create a Bundle**:
   - Navigate to BundleForge in Shopify Admin
   - Create a new bundle with 2+ products
   - Set a discount percentage and save

2. **Test Analytics**:
   - View the analytics dashboard
   - Check conversion metrics and performance data
   - Verify A/B testing placeholders

3. **Test Monitoring**:
   - Access the monitoring dashboard
   - Check system health and error tracking
   - Verify circuit breaker status

4. **Test Cart Transform** (if using Shopify Functions):
   - Add bundle products to cart on storefront
   - Verify bundle suggestions appear

5. **Test Discount Application** (if using Shopify Functions):
   - Complete bundle in cart
   - Verify discount is applied at checkout

### Running Tests

```bash
# Run all tests
cd apps/admin-remix
npm run test

# Run tests with coverage
npm run test:coverage

# Run specific test files
npm run test -- metafield.server.test.ts
```

### Health Checks

The app includes comprehensive health monitoring:
- **API Health**: `/api/health` endpoint
- **System Metrics**: Memory usage, uptime, response times
- **Circuit Breaker Status**: External service health
- **Error Tracking**: Structured logging with correlation IDs

## 🚀 **Production Features**

### ✅ **Core Functionality**
- **Bundle Management**: Complete CRUD operations for product bundles
- **Metafield Integration**: Custom metafield service with caching
- **Shopify Functions**: Cart transform and discount application (Rust-based)
- **Authentication**: Secure OAuth flow with session management
- **GDPR Compliance**: Built-in webhooks for data privacy

### ✅ **Production-Ready Features**
- **Analytics System**: Conversion tracking, performance metrics, A/B testing support
- **Monitoring Dashboard**: Real-time health checks, error tracking, system metrics
- **Advanced Caching**: Redis with in-memory fallback, cache invalidation strategies
- **Error Handling**: Structured logging, correlation IDs, circuit breaker patterns
- **Security**: Rate limiting, CSRF protection, security headers, webhook verification
- **Testing**: Comprehensive test suite with Vitest, mocking, and coverage reports

### ✅ **Deployment Ready**
- **Automated Session Secrets**: PowerShell and bash scripts for secure deployment
- **Environment Configuration**: Flexible config with sensible defaults
- **Health Monitoring**: `/api/health` endpoint for load balancer checks
- **Performance Optimization**: Caching, error recovery, and resilience patterns

## 📊 **Architecture Evolution**

| **Feature** | **Implementation** | **Status** |
|-------------|-------------------|------------|
| Bundle Management | Metafield-based with caching | ✅ Complete |
| Analytics | Conversion tracking + dashboard | ✅ Complete |
| Monitoring | Health checks + error tracking | ✅ Complete |
| Caching | Redis + in-memory fallback | ✅ Complete |
| Security | Rate limiting + CSRF + headers | ✅ Complete |
| Testing | Vitest + comprehensive coverage | ✅ Complete |
| Error Handling | Structured logging + circuit breakers | ✅ Complete |
| Deployment | Automated scripts + health checks | ✅ Complete |

The app is **production-ready** with enterprise-grade features and follows Shopify's best practices!

## 🔌 **API Endpoints**

### Admin Routes
- `/` - Dashboard with bundle overview and quick actions
- `/bundles` - Bundle management interface
- `/bundles/new` - Create new bundle
- `/bundles/:id` - View/edit specific bundle
- `/app/analytics` - Analytics dashboard with conversion metrics
- `/app/monitoring` - System monitoring and health status
- `/app/preferences` - App settings and configuration

### API Endpoints
- `/api/health` - Health check endpoint for monitoring
- `/api/analytics` - Analytics data API with filtering

### Webhook Endpoints
- `/webhooks/app/uninstalled` - App lifecycle management
- `/webhooks/gdpr` - GDPR compliance (data requests, redaction)
- `/webhooks/orders` - Order tracking for analytics
- `/webhooks/products` - Product sync for bundle management

## 🛡️ **Security Features**

- **Authentication**: Shopify OAuth with secure session management
- **CSRF Protection**: Built-in CSRF token validation
- **Rate Limiting**: Configurable request rate limiting
- **Security Headers**: Comprehensive security header implementation
- **Webhook Verification**: HMAC verification for all webhooks
- **Input Validation**: Zod schema validation for all data
- **Error Masking**: Production-safe error responses

## 📈 **Performance Features**

- **Caching**: Multi-layer caching with Redis and in-memory fallback
- **Circuit Breakers**: Automatic failure detection and recovery
- **Connection Pooling**: Optimized database connections
- **Lazy Loading**: Efficient resource loading strategies
- **Correlation IDs**: Request tracking for debugging and monitoring
