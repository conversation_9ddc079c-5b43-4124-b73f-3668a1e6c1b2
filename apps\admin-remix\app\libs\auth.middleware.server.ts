import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { requireAuth, getAuth } from "./auth.server";

export type AuthenticatedLoaderArgs = LoaderFunctionArgs & {
  auth: Awaited<ReturnType<typeof requireAuth>>;
};

export type AuthenticatedActionArgs = ActionFunctionArgs & {
  auth: Awaited<ReturnType<typeof requireAuth>>;
};

/**
 * Wrapper for loaders that require authentication
 */
export function authenticatedLoader<T>(
  loader: (args: AuthenticatedLoaderArgs) => T
) {
  return async (args: LoaderFunctionArgs): Promise<T> => {
    try {
      const auth = await requireAuth(args.request);
      return loader({ ...args, auth });
    } catch (error) {
      // If it's a redirect, throw it
      if (error instanceof Response) {
        throw error;
      }
      
      // Otherwise, redirect to auth
      const url = new URL(args.request.url);
      const shop = url.searchParams.get("shop");
      
      if (shop) {
        throw redirect(`/auth?shop=${shop}`);
      }
      
      // No shop parameter, show error
      throw new Response("Shop parameter required", { status: 400 });
    }
  };
}

/**
 * Wrapper for actions that require authentication
 */
export function authenticatedAction<T>(
  action: (args: AuthenticatedActionArgs) => T
) {
  return async (args: ActionFunctionArgs): Promise<T> => {
    try {
      const auth = await requireAuth(args.request);
      return action({ ...args, auth });
    } catch (error) {
      // If it's a redirect, throw it
      if (error instanceof Response) {
        throw error;
      }
      
      // Otherwise, return unauthorized
      throw new Response("Unauthorized", { status: 401 });
    }
  };
}

/**
 * Wrapper for loaders that optionally use authentication
 */
export function optionalAuthLoader<T>(
  loader: (args: LoaderFunctionArgs & { auth: Awaited<ReturnType<typeof getAuth>> }) => T
) {
  return async (args: LoaderFunctionArgs): Promise<T> => {
    const auth = await getAuth(args.request);
    return loader({ ...args, auth });
  };
}