use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BundleConfig {
    pub id: String,
    pub name: String,
    pub products: Vec<BundleProduct>,
    pub discount_percentage: f64,
    pub active: bool,
    pub ab_test_config: Option<ABTestConfig>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ABTestConfig {
    pub enabled: bool,
    pub test_id: String,
    pub variant_a: ABTestVariant,
    pub variant_b: ABTestVariant,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ABTestVariant {
    pub name: String,
    pub discount_percentage: f64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BundleProduct {
    pub product_id: String,
    pub variant_id: Option<String>,
    pub quantity: u32,
}

#[derive(Debug, <PERSON>lone)]
pub struct BundleMatch {
    pub bundle_id: String,
    pub bundle_name: String,
    pub matched_lines: Vec<MatchedLine>,
    pub missing_products: Vec<BundleProduct>,
    pub is_complete: bool,
    pub quantity_multiplier: u32,
    pub discount_percentage: f64,
}

#[derive(Debug, Clone)]
pub struct MatchedLine {
    pub line_id: String,
    pub product_id: String,
    pub variant_id: String,
    pub quantity_used: u32,
}

/// Parse metafields from GraphQL response with pagination support
pub fn parse_metafields<T>(metafields: &T) -> Vec<BundleConfig> 
where 
    T: MetafieldConnection
{
    let mut bundles = Vec::new();
    
    if let Some(edges) = metafields.edges() {
        for edge in edges {
            if let Some(node) = edge.node() {
                if node.namespace() == "bundleforge" && node.key().starts_with("bundle_config_") {
                    if let Some(value) = node.value() {
                        match serde_json::from_str::<BundleConfig>(value) {
                            Ok(bundle) if bundle.active => bundles.push(bundle),
                            Err(e) => eprintln!("Failed to parse bundle config: {}", e),
                            _ => {}
                        }
                    }
                }
            }
        }
    }
    
    bundles
}

/// Get customer ID from cart attributes for A/B test variant assignment
pub fn get_customer_id_from_cart<T>(metafields: &T) -> Option<String>
where
    T: MetafieldConnection
{
    if let Some(edges) = metafields.edges() {
        for edge in edges {
            if let Some(node) = edge.node() {
                if node.namespace() == "bundleforge" && node.key() == "customer_id" {
                    return node.value().map(|s| s.to_string());
                }
            }
        }
    }
    None
}

/// Determine A/B test variant based on customer ID
pub fn get_ab_test_variant(customer_id: &str, test_id: &str, traffic_allocation: f64) -> &'static str {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let mut hasher = DefaultHasher::new();
    format!("{}:{}", customer_id, test_id).hash(&mut hasher);
    let hash_value = hasher.finish();
    
    let threshold = (traffic_allocation / 100.0 * u64::MAX as f64) as u64;
    
    if hash_value <= threshold {
        "variant_a"
    } else {
        "variant_b"
    }
}

/// Match bundles against cart items with optimized O(n) complexity
pub fn match_bundles<C, L>(cart: &C, bundles: &[BundleConfig]) -> Vec<BundleMatch> 
where
    C: Cart,
    L: CartLine
{
    let mut matches = Vec::new();
    
    // Build index of cart items by product/variant for O(1) lookup
    let mut cart_index: HashMap<(String, Option<String>), Vec<(String, u32)>> = HashMap::new();
    
    if let Some(lines) = cart.lines() {
        for line in lines {
            if let Some(merchandise) = line.merchandise() {
                let product_id = merchandise.product_id();
                let variant_id = merchandise.variant_id();
                let line_id = line.id();
                let quantity = line.quantity() as u32;
                
                cart_index
                    .entry((product_id.clone(), Some(variant_id.clone())))
                    .or_insert_with(Vec::new)
                    .push((line_id.clone(), quantity));
                
                // Also index by product only for non-variant-specific bundles
                cart_index
                    .entry((product_id, None))
                    .or_insert_with(Vec::new)
                    .push((line_id, quantity));
            }
        }
    }
    
    // Check each bundle
    for bundle in bundles {
        let bundle_match = check_bundle_match(bundle, &cart_index);
        if bundle_match.quantity_multiplier > 0 {
            matches.push(bundle_match);
        }
    }
    
    // Sort by discount percentage (highest first) to handle conflicts
    matches.sort_by(|a, b| b.discount_percentage.partial_cmp(&a.discount_percentage).unwrap());
    
    matches
}

/// Match bundles against cart items with A/B test support
pub fn match_bundles_with_customer<C, L, T>(
    cart: &C, 
    bundles: &[BundleConfig], 
    metafields: &T
) -> Vec<BundleMatch>
where
    C: Cart,
    L: CartLine,
    T: MetafieldConnection
{
    let customer_id = get_customer_id_from_cart(metafields);
    let mut matches = Vec::new();
    
    // Build index of cart items by product/variant for O(1) lookup
    let mut cart_index: HashMap<(String, Option<String>), Vec<(String, u32)>> = HashMap::new();
    
    if let Some(lines) = cart.lines() {
        for line in lines {
            if let Some(merchandise) = line.merchandise() {
                let product_id = merchandise.product_id();
                let variant_id = merchandise.variant_id();
                let line_id = line.id();
                let quantity = line.quantity() as u32;
                
                cart_index
                    .entry((product_id.clone(), Some(variant_id.clone())))
                    .or_insert_with(Vec::new)
                    .push((line_id.clone(), quantity));
                
                // Also index by product only for non-variant-specific bundles
                cart_index
                    .entry((product_id, None))
                    .or_insert_with(Vec::new)
                    .push((line_id, quantity));
            }
        }
    }
    
    // Check each bundle
    for bundle in bundles {
        let mut bundle_match = check_bundle_match(bundle, &cart_index);
        
        // Apply A/B test variant discount if applicable
        if bundle_match.quantity_multiplier > 0 {
            if let Some(ab_config) = &bundle.ab_test_config {
                if ab_config.enabled {
                    if let Some(ref customer) = customer_id {
                        let variant = get_ab_test_variant(customer, &ab_config.test_id, 50.0); // Default 50/50 split
                        
                        if variant == "variant_b" {
                            bundle_match.discount_percentage = ab_config.variant_b.discount_percentage;
                        } else {
                            bundle_match.discount_percentage = ab_config.variant_a.discount_percentage;
                        }
                    }
                }
            }
            
            matches.push(bundle_match);
        }
    }
    
    // Sort by discount percentage (highest first) to handle conflicts
    matches.sort_by(|a, b| b.discount_percentage.partial_cmp(&a.discount_percentage).unwrap());
    
    matches
}

fn check_bundle_match(
    bundle: &BundleConfig, 
    cart_index: &HashMap<(String, Option<String>), Vec<(String, u32)>>
) -> BundleMatch {
    let mut matched_lines = Vec::new();
    let mut missing_products = Vec::new();
    let mut max_multiplier = u32::MAX;
    let mut quantities_available: HashMap<String, u32> = HashMap::new();
    
    // First pass: check availability
    for bundle_product in &bundle.products {
        let key = (bundle_product.product_id.clone(), bundle_product.variant_id.clone());
        
        if let Some(cart_lines) = cart_index.get(&key) {
            let total_quantity: u32 = cart_lines.iter().map(|(_, qty)| qty).sum();
            quantities_available.insert(bundle_product.product_id.clone(), total_quantity);
            
            let possible_bundles = total_quantity / bundle_product.quantity;
            max_multiplier = max_multiplier.min(possible_bundles);
        } else {
            // Try product-only match if no variant specified
            if bundle_product.variant_id.is_none() {
                let key = (bundle_product.product_id.clone(), None);
                if let Some(cart_lines) = cart_index.get(&key) {
                    let total_quantity: u32 = cart_lines.iter().map(|(_, qty)| qty).sum();
                    quantities_available.insert(bundle_product.product_id.clone(), total_quantity);
                    
                    let possible_bundles = total_quantity / bundle_product.quantity;
                    max_multiplier = max_multiplier.min(possible_bundles);
                } else {
                    missing_products.push(bundle_product.clone());
                    max_multiplier = 0;
                }
            } else {
                missing_products.push(bundle_product.clone());
                max_multiplier = 0;
            }
        }
    }
    
    // Second pass: allocate quantities for complete bundles
    if max_multiplier > 0 && max_multiplier != u32::MAX {
        for bundle_product in &bundle.products {
            let key = (bundle_product.product_id.clone(), bundle_product.variant_id.clone());
            let required_qty = bundle_product.quantity * max_multiplier;
            
            if let Some(cart_lines) = cart_index.get(&key) {
                let mut remaining = required_qty;
                for (line_id, available) in cart_lines {
                    let used = remaining.min(*available);
                    if used > 0 {
                        matched_lines.push(MatchedLine {
                            line_id: line_id.clone(),
                            product_id: bundle_product.product_id.clone(),
                            variant_id: bundle_product.variant_id.clone().unwrap_or_default(),
                            quantity_used: used,
                        });
                        remaining -= used;
                        if remaining == 0 {
                            break;
                        }
                    }
                }
            }
        }
    }
    
    BundleMatch {
        bundle_id: bundle.id.clone(),
        bundle_name: bundle.name.clone(),
        matched_lines,
        missing_products,
        is_complete: missing_products.is_empty() && max_multiplier > 0,
        quantity_multiplier: if max_multiplier == u32::MAX { 0 } else { max_multiplier },
        discount_percentage: bundle.discount_percentage,
    }
}

// Trait definitions for generic GraphQL types
pub trait MetafieldConnection {
    type Edge: MetafieldEdge;
    fn edges(&self) -> Option<&Vec<Self::Edge>>;
}

pub trait MetafieldEdge {
    type Node: MetafieldNode;
    fn node(&self) -> Option<&Self::Node>;
}

pub trait MetafieldNode {
    fn namespace(&self) -> &str;
    fn key(&self) -> &str;
    fn value(&self) -> Option<&str>;
}

pub trait Cart {
    type Line: CartLine;
    fn lines(&self) -> Option<&Vec<Self::Line>>;
}

pub trait CartLine {
    type Merchandise: Merchandise;
    fn id(&self) -> &str;
    fn quantity(&self) -> i64;
    fn merchandise(&self) -> Option<&Self::Merchandise>;
}

pub trait Merchandise {
    fn product_id(&self) -> &str;
    fn variant_id(&self) -> &str;
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_bundle_matching() {
        // Test implementation here
    }
}