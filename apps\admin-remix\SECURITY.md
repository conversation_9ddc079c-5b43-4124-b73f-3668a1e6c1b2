# BundleForge Security Documentation

## Overview

This document outlines the security measures implemented in the BundleForge Shopify app to protect against common vulnerabilities and ensure data privacy.

## Security Features Implemented

### 1. Authentication & Authorization

#### OAuth State Storage (Fixed)
- **Previous Issue**: OAuth state was stored in memory using a Map, vulnerable to memory exhaustion attacks
- **Solution**: Implemented Redis-based storage with automatic TTL (10 minutes)
- **File**: `/app/libs/auth.server.ts`, `/app/libs/redis.server.ts`

#### Session Security
- **Enhanced session ID generation** with timestamp and random components to prevent session fixation
- **Token validation** includes expiration checks, audience validation, and issuer verification
- **Secure session storage** using Redis with 24-hour TTL
- **Files**: `/app/libs/auth.server.ts`, `/app/libs/session-storage.server.ts`

#### Rate Limiting
- **Auth endpoints**: 5 attempts per 5 minutes
- **API endpoints**: 60 requests per minute
- **Bundle creation**: 100 per hour
- **Webhooks**: 10 per second
- **Implementation**: Sliding window algorithm using Redis sorted sets
- **File**: `/app/libs/rate-limit.server.ts`

### 2. Input Validation & XSS Protection

#### Enhanced Sanitization
- **DOMPurify integration** for comprehensive XSS protection
- **HTML entity encoding** for special characters
- **Regex validation** for Shopify-specific formats (shop domains, product IDs)
- **Request size limits** to prevent DoS attacks
- **File**: `/app/libs/validation.server.ts`

#### CSRF Protection
- **Token generation** using crypto.randomBytes(32)
- **Double-submit cookie pattern** with httpOnly cookies
- **Automatic validation** for all state-changing requests
- **File**: `/app/libs/csrf.server.ts`

### 3. Security Headers

Comprehensive security headers implemented:
- **Content Security Policy (CSP)** tailored for Shopify embedded apps
- **X-Frame-Options** allowing only Shopify admin
- **Strict-Transport-Security (HSTS)** with preload
- **X-Content-Type-Options**: nosniff
- **Referrer-Policy**: strict-origin-when-cross-origin
- **File**: `/app/libs/security-headers.server.ts`

### 4. Data Protection

#### Webhook Security
- **HMAC verification** for all webhooks
- **Replay attack prevention** with 60-second timestamp window (reduced from 5 minutes)
- **Request validation** using Zod schemas
- **Files**: Various webhook routes in `/app/routes/webhooks.*`

#### Environment Variable Encryption
- **AES-256-GCM encryption** for sensitive environment variables
- **Secure key derivation** using scrypt
- **Automatic decryption** at runtime
- **Files**: `/app/libs/env-encryption.server.ts`, `/scripts/encrypt-env.js`

#### Logging Security
- **Sensitive data redaction** in all log statements
- **No logging of**: shop domains, customer IDs, financial data, API keys
- **Structured logging** with correlation IDs for debugging without exposing sensitive data

### 5. Error Handling

- **Custom error classes** for different error types
- **Circuit breaker pattern** for external service calls
- **Retry logic** with exponential backoff
- **Sanitized error messages** in production
- **File**: `/app/libs/error-handler.server.ts`

## Security Best Practices

### Development

1. **Never commit secrets** to version control
2. **Use encrypted environment variables** in production
3. **Run security audits** regularly: `npm audit`
4. **Keep dependencies updated**
5. **Use TypeScript** for type safety

### Deployment

1. **Set ENCRYPTION_MASTER_KEY** in production environment
2. **Use Redis** for session storage (not in-memory fallback)
3. **Enable HTTPS** everywhere
4. **Configure proper CORS** settings
5. **Monitor rate limits** and adjust as needed

### Environment Variables

Required environment variables (encrypt in production):
```bash
SHOPIFY_API_KEY=<encrypted>
SHOPIFY_API_SECRET=<encrypted>
REDIS_URL=<encrypted>
ENCRYPTION_MASTER_KEY=<32-byte-hex-string>
```

To encrypt a variable:
```bash
node scripts/encrypt-env.js SHOPIFY_API_SECRET "your-secret-value"
```

## GDPR Compliance

- **Data request handling** implemented in webhook handlers
- **Data deletion** support for both customer and shop data
- **Metafield cleanup** on app uninstall
- **Audit trail** capability through structured logging

## Security Checklist

- [x] OAuth state storage using Redis
- [x] Rate limiting on all endpoints
- [x] CSRF protection on forms
- [x] XSS protection with DOMPurify
- [x] Security headers configured
- [x] Webhook replay attack prevention
- [x] Session security enhancements
- [x] Sensitive data removed from logs
- [x] Environment variable encryption
- [x] GDPR compliance webhooks
- [x] Error message sanitization
- [x] Request size limits

## Incident Response

1. **Detection**: Monitor logs for suspicious patterns
2. **Containment**: Use rate limiting and circuit breakers
3. **Investigation**: Use correlation IDs to trace issues
4. **Remediation**: Deploy fixes using secure deployment pipeline
5. **Documentation**: Update this security documentation

## Regular Security Tasks

- **Weekly**: Review security logs for anomalies
- **Monthly**: Update dependencies and run security audits
- **Quarterly**: Review and update rate limits
- **Annually**: Conduct full security audit

## Contact

For security concerns or to report vulnerabilities, please contact the security team immediately.

---

Last Updated: [Current Date]
Security Review: Required before each major release