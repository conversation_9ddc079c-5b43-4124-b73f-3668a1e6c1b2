import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  InlineStack,
  Badge,
  DataTable,
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Select,
} from "@shopify/polaris";
import { authenticate } from "../libs/auth.server";
import { 
  getAnalyticsData, 
  getAllBundles,
  type AnalyticsData,
  type BundleConfig 
} from "../libs/metafield.server";
import { handleError } from "../libs/error-handler.server";
import { useState } from "react";

interface AnalyticsLoaderData {
  analytics: AnalyticsData[];
  bundles: BundleConfig[];
  totalRevenue: number;
  totalConversions: number;
  totalViews: number;
  conversionRate: number;
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);
    
    // Get analytics data and bundles
    const [analytics, bundles] = await Promise.all([
      getAnalyticsData(session),
      getAllBundles(session)
    ]);
    
    // Calculate summary metrics
    const totalRevenue = analytics.reduce((sum, data) => sum + data.revenue, 0);
    const totalConversions = analytics.reduce((sum, data) => sum + data.conversions, 0);
    const totalViews = analytics.reduce((sum, data) => sum + data.views, 0);
    const conversionRate = totalViews > 0 ? (totalConversions / totalViews) * 100 : 0;
    
    return json({
      analytics,
      bundles,
      totalRevenue,
      totalConversions,
      totalViews,
      conversionRate,
    });
  } catch (error) {
    console.error("Error loading analytics:", error);
    return handleError(error);
  }
}

export default function Analytics() {
  const { 
    analytics, 
    bundles, 
    totalRevenue, 
    totalConversions, 
    totalViews, 
    conversionRate 
  } = useLoaderData<AnalyticsLoaderData>();
  
  const [selectedTimeRange, setSelectedTimeRange] = useState("30");
  const [selectedBundle, setSelectedBundle] = useState("all");
  
  // Create data table rows
  const tableRows = analytics.map((data) => {
    const bundle = bundles.find(b => b.id === data.bundle_id);
    const bundleName = bundle ? bundle.name : `Bundle ${data.bundle_id}`;
    const bundleStatus = bundle ? (bundle.active ? "Active" : "Inactive") : "Unknown";
    const bundleConversionRate = data.views > 0 ? ((data.conversions / data.views) * 100).toFixed(2) : "0.00";
    
    return [
      bundleName,
      <Badge key="status" tone={bundleStatus === "Active" ? "success" : "critical"}>
        {bundleStatus}
      </Badge>,
      data.views.toLocaleString(),
      data.cart_adds.toLocaleString(),
      data.conversions.toLocaleString(),
      `${bundleConversionRate}%`,
      `$${data.revenue.toFixed(2)}`,
      new Date(data.last_updated).toLocaleDateString(),
    ];
  });
  
  const timeRangeOptions = [
    { label: "Last 7 days", value: "7" },
    { label: "Last 30 days", value: "30" },
    { label: "Last 90 days", value: "90" },
    { label: "All time", value: "all" },
  ];
  
  const bundleOptions = [
    { label: "All bundles", value: "all" },
    ...bundles.map(bundle => ({
      label: bundle.name,
      value: bundle.id,
    })),
  ];

  return (
    <Page
      title="Bundle Analytics"
      subtitle="Track performance and conversion metrics for your bundles"
      primaryAction={{
        content: "Export Data",
        onAction: () => {
          // TODO: Implement data export functionality
          console.log("Export analytics data");
        },
      }}
    >
      <Layout>
        <Layout.Section>
          <InlineStack gap="400">
            <div style={{ minWidth: "200px" }}>
              <Select
                label="Time Range"
                options={timeRangeOptions}
                value={selectedTimeRange}
                onChange={setSelectedTimeRange}
              />
            </div>
            <div style={{ minWidth: "200px" }}>
              <Select
                label="Bundle"
                options={bundleOptions}
                value={selectedBundle}
                onChange={setSelectedBundle}
              />
            </div>
          </InlineStack>
        </Layout.Section>
        
        <Layout.Section>
          <InlineStack gap="400">
            <Card>
              <BlockStack gap="200">
                <Text variant="headingMd" as="h3">Total Revenue</Text>
                <Text variant="heading2xl" as="p" tone="success">
                  ${totalRevenue.toFixed(2)}
                </Text>
              </BlockStack>
            </Card>
            
            <Card>
              <BlockStack gap="200">
                <Text variant="headingMd" as="h3">Conversions</Text>
                <Text variant="heading2xl" as="p">
                  {totalConversions.toLocaleString()}
                </Text>
              </BlockStack>
            </Card>
            
            <Card>
              <BlockStack gap="200">
                <Text variant="headingMd" as="h3">Bundle Views</Text>
                <Text variant="heading2xl" as="p">
                  {totalViews.toLocaleString()}
                </Text>
              </BlockStack>
            </Card>
            
            <Card>
              <BlockStack gap="200">
                <Text variant="headingMd" as="h3">Conversion Rate</Text>
                <Text variant="heading2xl" as="p" tone={conversionRate > 5 ? "success" : "warning"}>
                  {conversionRate.toFixed(2)}%
                </Text>
              </BlockStack>
            </Card>
          </InlineStack>
        </Layout.Section>
        
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingLg" as="h2">Bundle Performance</Text>
              
              {analytics.length === 0 ? (
                <Banner tone="info">
                  <p>No analytics data available yet. Bundle performance will appear here once customers start interacting with your bundles.</p>
                </Banner>
              ) : (
                <DataTable
                  columnContentTypes={[
                    'text',
                    'text', 
                    'numeric',
                    'numeric',
                    'numeric',
                    'numeric',
                    'numeric',
                    'text',
                  ]}
                  headings={[
                    'Bundle Name',
                    'Status',
                    'Views',
                    'Cart Adds',
                    'Conversions',
                    'Conversion Rate',
                    'Revenue',
                    'Last Updated',
                  ]}
                  rows={tableRows}
                  footerContent={`Showing ${analytics.length} bundle${analytics.length !== 1 ? 's' : ''}`}
                />
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
        
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text variant="headingLg" as="h2">A/B Testing</Text>
              
              <Banner tone="info">
                <p>A/B testing functionality will be available here. You'll be able to test different bundle configurations, pricing strategies, and promotional offers to optimize conversion rates.</p>
              </Banner>
              
              <Button disabled>
                Create A/B Test
              </Button>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
