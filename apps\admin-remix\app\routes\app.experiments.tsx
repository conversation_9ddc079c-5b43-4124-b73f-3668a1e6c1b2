import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from '@remix-run/node';
import { useLoaderData, useSubmit, useNavigation, Form, useActionData } from '@remix-run/react';
import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  Button,
  DataTable,
  Badge,
  InlineStack,
  Select,
  TextField,
  Modal,
  FormLayout,
  Checkbox,
  RangeSlider,
  Divider,
  Banner,
  ProgressBar,
  Tooltip,
  Icon,
} from '@shopify/polaris';
import { InfoIcon, EditIcon, DeleteIcon, PauseCircleIcon, PlayIcon, DuplicateIcon } from '@shopify/polaris-icons';
import { authenticate } from '~/shopify.server';
import { BundleService } from '~/libs/bundle.server';
import { AnalyticsService } from '~/libs/analytics.server';
import { useState, useCallback } from 'react';
import { z } from 'zod';
import type { Bundle, ABTestConfig } from '~/libs/metafield.server';

const ABTestSchema = z.object({
  name: z.string().min(1, 'Test name is required'),
  bundle_id: z.string().min(1, 'Bundle selection is required'),
  traffic_allocation: z.number().min(10).max(90),
  variant_a: z.object({
    name: z.string().default('Control'),
    discount_percentage: z.number().min(0).max(100),
  }),
  variant_b: z.object({
    name: z.string().default('Variant'),
    discount_percentage: z.number().min(0).max(100),
  }),
  success_metric: z.enum(['conversion_rate', 'revenue', 'aov']),
  minimum_sample_size: z.number().min(100).default(1000),
  auto_end: z.boolean().default(false),
});

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const bundleService = new BundleService(shop, admin.graphql);
  const analyticsService = new AnalyticsService(shop);
  
  // Get all bundles for the dropdown
  const bundles = await bundleService.getAllBundles();
  
  // Get all A/B tests
  const abTests = await analyticsService.getABTests();
  
  // Get active test count
  const activeTests = abTests.filter(test => test.status === 'active').length;
  
  return json({
    bundles,
    abTests,
    activeTests,
    shop,
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const formData = await request.formData();
  const action = formData.get('_action');
  
  const bundleService = new BundleService(shop, admin.graphql);
  const analyticsService = new AnalyticsService(shop);
  
  try {
    switch (action) {
      case 'create': {
        const testData = {
          name: formData.get('name') as string,
          bundle_id: formData.get('bundle_id') as string,
          traffic_allocation: Number(formData.get('traffic_allocation')),
          variant_a: {
            name: formData.get('variant_a_name') as string || 'Control',
            discount_percentage: Number(formData.get('variant_a_discount')),
          },
          variant_b: {
            name: formData.get('variant_b_name') as string || 'Variant',
            discount_percentage: Number(formData.get('variant_b_discount')),
          },
          success_metric: formData.get('success_metric') as 'conversion_rate' | 'revenue' | 'aov',
          minimum_sample_size: Number(formData.get('minimum_sample_size')) || 1000,
          auto_end: formData.get('auto_end') === 'true',
        };
        
        const validated = ABTestSchema.parse(testData);
        const abTest = await analyticsService.createABTest(validated);
        
        // Update bundle with A/B test config
        const bundle = await bundleService.getBundleById(validated.bundle_id);
        if (bundle) {
          await bundleService.updateBundle(bundle.id, {
            ...bundle,
            ab_test_config: {
              enabled: true,
              test_id: abTest.id,
              variant_a: validated.variant_a,
              variant_b: validated.variant_b,
            },
          });
        }
        
        return json({ success: true, message: 'A/B test created successfully' });
      }
      
      case 'pause': {
        const testId = formData.get('test_id') as string;
        await analyticsService.updateABTestStatus(testId, 'paused');
        return json({ success: true, message: 'A/B test paused' });
      }
      
      case 'resume': {
        const testId = formData.get('test_id') as string;
        await analyticsService.updateABTestStatus(testId, 'active');
        return json({ success: true, message: 'A/B test resumed' });
      }
      
      case 'end': {
        const testId = formData.get('test_id') as string;
        const test = await analyticsService.getABTestById(testId);
        
        if (test) {
          // Calculate final results
          const results = await analyticsService.calculateABTestResults(testId);
          await analyticsService.updateABTestStatus(testId, 'completed', results);
          
          // Disable A/B test on bundle
          const bundle = await bundleService.getBundleById(test.bundle_id);
          if (bundle) {
            await bundleService.updateBundle(bundle.id, {
              ...bundle,
              ab_test_config: undefined,
            });
          }
        }
        
        return json({ success: true, message: 'A/B test ended' });
      }
      
      case 'delete': {
        const testId = formData.get('test_id') as string;
        await analyticsService.deleteABTest(testId);
        return json({ success: true, message: 'A/B test deleted' });
      }
      
      case 'duplicate': {
        const testId = formData.get('test_id') as string;
        const test = await analyticsService.getABTestById(testId);
        
        if (test) {
          const newTest = await analyticsService.createABTest({
            ...test,
            name: `${test.name} (Copy)`,
            status: 'draft',
          });
          return json({ success: true, message: 'A/B test duplicated' });
        }
        break;
      }
    }
  } catch (error) {
    console.error('A/B test action error:', error);
    return json(
      { success: false, message: error instanceof Error ? error.message : 'An error occurred' },
      { status: 400 }
    );
  }
  
  return json({ success: false, message: 'Invalid action' }, { status: 400 });
};

export default function Experiments() {
  const { bundles, abTests, activeTests } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';
  
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTest, setSelectedTest] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  
  // Form state
  const [testName, setTestName] = useState('');
  const [selectedBundle, setSelectedBundle] = useState('');
  const [trafficAllocation, setTrafficAllocation] = useState(50);
  const [variantAName, setVariantAName] = useState('Control');
  const [variantADiscount, setVariantADiscount] = useState('');
  const [variantBName, setVariantBName] = useState('Variant');
  const [variantBDiscount, setVariantBDiscount] = useState('');
  const [successMetric, setSuccessMetric] = useState('conversion_rate');
  const [minimumSampleSize, setMinimumSampleSize] = useState('1000');
  const [autoEnd, setAutoEnd] = useState(false);
  
  const handleCreateTest = useCallback(() => {
    const formData = new FormData();
    formData.append('_action', 'create');
    formData.append('name', testName);
    formData.append('bundle_id', selectedBundle);
    formData.append('traffic_allocation', trafficAllocation.toString());
    formData.append('variant_a_name', variantAName);
    formData.append('variant_a_discount', variantADiscount);
    formData.append('variant_b_name', variantBName);
    formData.append('variant_b_discount', variantBDiscount);
    formData.append('success_metric', successMetric);
    formData.append('minimum_sample_size', minimumSampleSize);
    formData.append('auto_end', autoEnd.toString());
    
    submit(formData, { method: 'post' });
    setShowCreateModal(false);
    resetForm();
  }, [testName, selectedBundle, trafficAllocation, variantAName, variantADiscount, 
      variantBName, variantBDiscount, successMetric, minimumSampleSize, autoEnd, submit]);
  
  const resetForm = () => {
    setTestName('');
    setSelectedBundle('');
    setTrafficAllocation(50);
    setVariantAName('Control');
    setVariantADiscount('');
    setVariantBName('Variant');
    setVariantBDiscount('');
    setSuccessMetric('conversion_rate');
    setMinimumSampleSize('1000');
    setAutoEnd(false);
  };
  
  const handleAction = (action: string, testId: string) => {
    const formData = new FormData();
    formData.append('_action', action);
    formData.append('test_id', testId);
    submit(formData, { method: 'post' });
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge tone="success">Active</Badge>;
      case 'paused':
        return <Badge tone="attention">Paused</Badge>;
      case 'completed':
        return <Badge>Completed</Badge>;
      case 'draft':
        return <Badge tone="info">Draft</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };
  
  const getConfidenceLevel = (test: any) => {
    if (!test.results || test.status !== 'active') return null;
    
    const confidence = test.results.statistical_significance || 0;
    let tone: 'critical' | 'warning' | 'success' = 'critical';
    
    if (confidence >= 95) tone = 'success';
    else if (confidence >= 90) tone = 'warning';
    
    return (
      <BlockStack gap="100">
        <Text variant="bodyMd" as="span">Statistical Confidence</Text>
        <ProgressBar progress={confidence} tone={tone} size="small" />
        <Text variant="bodySm" as="span" tone="subdued">{confidence.toFixed(1)}%</Text>
      </BlockStack>
    );
  };
  
  const tableRows = abTests.map((test) => [
    test.name,
    test.bundle_name || 'Unknown Bundle',
    getStatusBadge(test.status),
    test.variant_a?.name || 'Control',
    test.variant_b?.name || 'Variant',
    `${test.participants || 0}`,
    test.status === 'completed' && test.results ? (
      <Badge tone={test.results.winner === 'variant_b' ? 'success' : 'attention'}>
        {test.results.winner === 'variant_b' ? 'Variant Won' : 'No Clear Winner'}
      </Badge>
    ) : (
      <Text variant="bodySm" as="span" tone="subdued">—</Text>
    ),
    <InlineStack gap="200">
      <Button
        size="slim"
        icon={InfoIcon}
        onClick={() => {
          setSelectedTest(test);
          setShowDetailsModal(true);
        }}
      />
      {test.status === 'active' && (
        <Button
          size="slim"
          icon={PauseCircleIcon}
          onClick={() => handleAction('pause', test.id)}
        />
      )}
      {test.status === 'paused' && (
        <Button
          size="slim"
          icon={PlayIcon}
          onClick={() => handleAction('resume', test.id)}
        />
      )}
      {(test.status === 'active' || test.status === 'paused') && (
        <Button
          size="slim"
          tone="critical"
          onClick={() => handleAction('end', test.id)}
        >
          End Test
        </Button>
      )}
      <Button
        size="slim"
        icon={DuplicateIcon}
        onClick={() => handleAction('duplicate', test.id)}
      />
    </InlineStack>
  ]);
  
  return (
    <Page
      title="A/B Testing"
      subtitle="Test different bundle configurations to optimize performance"
      primaryAction={{
        content: 'Create A/B Test',
        onAction: () => setShowCreateModal(true),
        disabled: bundles.length === 0,
      }}
    >
      <Layout>
        {actionData?.message && (
          <Layout.Section>
            <Banner
              tone={actionData.success ? 'success' : 'critical'}
              onDismiss={() => {}}
            >
              <p>{actionData.message}</p>
            </Banner>
          </Layout.Section>
        )}
        
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text variant="headingMd" as="h2">Test Overview</Text>
                <Badge>{activeTests} Active {activeTests === 1 ? 'Test' : 'Tests'}</Badge>
              </InlineStack>
              
              {abTests.length === 0 ? (
                <Banner>
                  <p>No A/B tests yet. Create your first test to start optimizing bundle performance.</p>
                </Banner>
              ) : (
                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text', 'numeric', 'text', 'text']}
                  headings={[
                    'Test Name',
                    'Bundle',
                    'Status',
                    'Control',
                    'Variant',
                    'Participants',
                    'Result',
                    'Actions',
                  ]}
                  rows={tableRows}
                />
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
        
        {activeTests > 0 && (
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">Active Tests Performance</Text>
                {abTests
                  .filter(test => test.status === 'active')
                  .map(test => (
                    <Card key={test.id} sectioned>
                      <BlockStack gap="300">
                        <InlineStack align="space-between">
                          <Text variant="headingMd" as="h3">{test.name}</Text>
                          {getConfidenceLevel(test)}
                        </InlineStack>
                        
                        <Divider />
                        
                        <InlineStack gap="800" align="space-between">
                          <BlockStack gap="200">
                            <Text variant="headingSm" as="h4">{test.variant_a?.name}</Text>
                            <Text variant="bodyLg" as="p">{test.variant_a?.discount_percentage}% discount</Text>
                            <InlineStack gap="400">
                              <BlockStack gap="100">
                                <Text variant="bodySm" as="span" tone="subdued">Conversion</Text>
                                <Text variant="bodyLg" as="p">
                                  {((test.results?.variant_a_conversions || 0) / (test.results?.variant_a_views || 1) * 100).toFixed(2)}%
                                </Text>
                              </BlockStack>
                              <BlockStack gap="100">
                                <Text variant="bodySm" as="span" tone="subdued">Revenue</Text>
                                <Text variant="bodyLg" as="p">
                                  ${(test.results?.variant_a_revenue || 0).toFixed(2)}
                                </Text>
                              </BlockStack>
                            </InlineStack>
                          </BlockStack>
                          
                          <BlockStack gap="200">
                            <Text variant="headingSm" as="h4">{test.variant_b?.name}</Text>
                            <Text variant="bodyLg" as="p">{test.variant_b?.discount_percentage}% discount</Text>
                            <InlineStack gap="400">
                              <BlockStack gap="100">
                                <Text variant="bodySm" as="span" tone="subdued">Conversion</Text>
                                <Text variant="bodyLg" as="p">
                                  {((test.results?.variant_b_conversions || 0) / (test.results?.variant_b_views || 1) * 100).toFixed(2)}%
                                </Text>
                              </BlockStack>
                              <BlockStack gap="100">
                                <Text variant="bodySm" as="span" tone="subdued">Revenue</Text>
                                <Text variant="bodyLg" as="p">
                                  ${(test.results?.variant_b_revenue || 0).toFixed(2)}
                                </Text>
                              </BlockStack>
                            </InlineStack>
                          </BlockStack>
                        </InlineStack>
                        
                        {test.results?.recommendation && (
                          <Banner tone="info">
                            <p>{test.results.recommendation}</p>
                          </Banner>
                        )}
                      </BlockStack>
                    </Card>
                  ))}
              </BlockStack>
            </Card>
          </Layout.Section>
        )}
      </Layout>
      
      <Modal
        open={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
        }}
        title="Create A/B Test"
        primaryAction={{
          content: 'Create Test',
          onAction: handleCreateTest,
          loading: isSubmitting,
          disabled: !testName || !selectedBundle || !variantADiscount || !variantBDiscount,
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => {
              setShowCreateModal(false);
              resetForm();
            },
          },
        ]}
      >
        <Modal.Section>
          <FormLayout>
            <TextField
              label="Test Name"
              value={testName}
              onChange={setTestName}
              placeholder="e.g., Holiday Bundle Discount Test"
              autoComplete="off"
            />
            
            <Select
              label="Bundle"
              options={[
                { label: 'Select a bundle', value: '' },
                ...bundles.map(bundle => ({
                  label: bundle.name,
                  value: bundle.id,
                })),
              ]}
              value={selectedBundle}
              onChange={setSelectedBundle}
            />
            
            <BlockStack gap="200">
              <Text variant="headingSm" as="h3">Variant Configuration</Text>
              
              <BlockStack gap="400">
                <Card>
                  <BlockStack gap="200">
                    <TextField
                      label="Control Name"
                      value={variantAName}
                      onChange={setVariantAName}
                      autoComplete="off"
                    />
                    <TextField
                      label="Control Discount %"
                      type="number"
                      value={variantADiscount}
                      onChange={setVariantADiscount}
                      min="0"
                      max="100"
                      autoComplete="off"
                    />
                  </BlockStack>
                </Card>
                
                <Card>
                  <BlockStack gap="200">
                    <TextField
                      label="Variant Name"
                      value={variantBName}
                      onChange={setVariantBName}
                      autoComplete="off"
                    />
                    <TextField
                      label="Variant Discount %"
                      type="number"
                      value={variantBDiscount}
                      onChange={setVariantBDiscount}
                      min="0"
                      max="100"
                      autoComplete="off"
                    />
                  </BlockStack>
                </Card>
              </BlockStack>
            </BlockStack>
            
            <RangeSlider
              label={`Traffic Allocation: ${trafficAllocation}% to Control / ${100 - trafficAllocation}% to Variant`}
              value={trafficAllocation}
              onChange={setTrafficAllocation}
              min={10}
              max={90}
              step={10}
              output
            />
            
            <Select
              label="Success Metric"
              options={[
                { label: 'Conversion Rate', value: 'conversion_rate' },
                { label: 'Revenue', value: 'revenue' },
                { label: 'Average Order Value', value: 'aov' },
              ]}
              value={successMetric}
              onChange={setSuccessMetric}
            />
            
            <TextField
              label="Minimum Sample Size"
              type="number"
              value={minimumSampleSize}
              onChange={setMinimumSampleSize}
              min="100"
              helpText="Test will not show results until this many participants"
              autoComplete="off"
            />
            
            <Checkbox
              label="Automatically end test when statistically significant"
              checked={autoEnd}
              onChange={setAutoEnd}
            />
          </FormLayout>
        </Modal.Section>
      </Modal>
      
      <Modal
        open={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedTest(null);
        }}
        title={selectedTest?.name || 'Test Details'}
        secondaryActions={[
          {
            content: 'Close',
            onAction: () => {
              setShowDetailsModal(false);
              setSelectedTest(null);
            },
          },
        ]}
      >
        {selectedTest && (
          <Modal.Section>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text variant="headingSm" as="h3">Status</Text>
                {getStatusBadge(selectedTest.status)}
              </InlineStack>
              
              <Divider />
              
              <BlockStack gap="200">
                <Text variant="headingSm" as="h3">Configuration</Text>
                <Text variant="bodyMd" as="p">Bundle: {selectedTest.bundle_name}</Text>
                <Text variant="bodyMd" as="p">Success Metric: {selectedTest.success_metric}</Text>
                <Text variant="bodyMd" as="p">Traffic Split: {selectedTest.traffic_allocation}% / {100 - selectedTest.traffic_allocation}%</Text>
                <Text variant="bodyMd" as="p">Sample Size Goal: {selectedTest.minimum_sample_size}</Text>
              </BlockStack>
              
              {selectedTest.results && (
                <>
                  <Divider />
                  
                  <BlockStack gap="200">
                    <Text variant="headingSm" as="h3">Results</Text>
                    <Text variant="bodyMd" as="p">Total Participants: {selectedTest.participants}</Text>
                    <Text variant="bodyMd" as="p">Statistical Significance: {selectedTest.results.statistical_significance?.toFixed(1)}%</Text>
                    <Text variant="bodyMd" as="p">
                      Lift: {selectedTest.results.lift > 0 ? '+' : ''}{selectedTest.results.lift?.toFixed(1)}%
                    </Text>
                    {selectedTest.results.recommendation && (
                      <Banner tone="info">
                        <p>{selectedTest.results.recommendation}</p>
                      </Banner>
                    )}
                  </BlockStack>
                </>
              )}
              
              <Divider />
              
              <BlockStack gap="200">
                <Text variant="headingSm" as="h3">Timeline</Text>
                <Text variant="bodyMd" as="p">Created: {new Date(selectedTest.created_at).toLocaleDateString()}</Text>
                {selectedTest.started_at && (
                  <Text variant="bodyMd" as="p">Started: {new Date(selectedTest.started_at).toLocaleDateString()}</Text>
                )}
                {selectedTest.ended_at && (
                  <Text variant="bodyMd" as="p">Ended: {new Date(selectedTest.ended_at).toLocaleDateString()}</Text>
                )}
              </BlockStack>
            </BlockStack>
          </Modal.Section>
        )}
      </Modal>
    </Page>
  );
}