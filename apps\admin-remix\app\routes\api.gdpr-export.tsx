import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticatedLoader } from "~/libs/auth.middleware.server";
import { handleDataRequest } from "~/libs/auth.server";
import { z } from "zod";
import { formatZodError } from "~/libs/validation.server";
import { Logger } from "~/libs/error-handler.server";

const logger = Logger.getInstance();

const ExportParamsSchema = z.object({
  customerId: z.string().optional(),
  format: z.enum(["json", "csv"]).default("json"),
});

export const loader = authenticatedLoader(async ({ request, auth }) => {
  const url = new URL(request.url);
  
  try {
    const params = ExportParamsSchema.parse({
      customerId: url.searchParams.get("customerId") || undefined,
      format: url.searchParams.get("format") || "json",
    });
    
    const correlationId = logger.info("GDPR data export requested", {
      shop: auth.session.shop,
      metadata: params
    });
    
    // Collect all data for the shop or specific customer
    const data = await handleDataRequest(
      auth.session.shop,
      params.customerId
    );
    
    // Format the response based on requested format
    if (params.format === "csv") {
      const csv = convertToCSV(data);
      
      return new Response(csv, {
        status: 200,
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="bundleforge-data-export-${Date.now()}.csv"`,
          "X-Correlation-Id": correlationId,
        },
      });
    }
    
    // Default to JSON
    return json(data, {
      headers: {
        "Content-Type": "application/json",
        "Content-Disposition": `attachment; filename="bundleforge-data-export-${Date.now()}.json"`,
        "X-Correlation-Id": correlationId,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return json(
        { error: "Invalid parameters", details: formatZodError(error) },
        { status: 400 }
      );
    }
    
    logger.error("GDPR export failed", error, {
      shop: auth.session.shop,
      metadata: { url: url.toString() }
    });
    
    return json(
      { error: "Failed to export data" },
      { status: 500 }
    );
  }
});

function convertToCSV(data: any): string {
  const lines: string[] = [];
  
  // Header
  lines.push("Data Export for BundleForge");
  lines.push(`Shop: ${data.shop}`);
  lines.push(`Customer ID: ${data.customerId || "All Customers"}`);
  lines.push(`Export Date: ${new Date().toISOString()}`);
  lines.push("");
  
  // Bundles
  if (data.data.bundles && data.data.bundles.length > 0) {
    lines.push("Bundles");
    lines.push("Bundle ID,Bundle Name,Last Updated");
    data.data.bundles.forEach((bundle: any) => {
      lines.push(`${bundle.bundleId},${bundle.bundleName},${bundle.lastUpdated}`);
    });
    lines.push("");
  }
  
  // Order Bundles
  if (data.data.orderBundles && data.data.orderBundles.length > 0) {
    lines.push("Order History");
    lines.push("Order ID,Order Date,Product Title,Bundle ID");
    data.data.orderBundles.forEach((order: any) => {
      order.bundleItems.forEach((item: any) => {
        lines.push(`${order.orderId},${order.orderDate},${item.productTitle},${item.bundleId || "N/A"}`);
      });
    });
    lines.push("");
  }
  
  // Analytics Summary
  if (data.data.analytics) {
    lines.push("Analytics Summary");
    lines.push(`Total Bundle Orders: ${data.data.analytics.totalBundleOrders}`);
    lines.push(`First Bundle Order: ${data.data.analytics.firstBundleOrder || "N/A"}`);
    lines.push(`Last Bundle Order: ${data.data.analytics.lastBundleOrder || "N/A"}`);
  }
  
  return lines.join("\n");
}