import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook, handleDataDeletion } from "../libs/auth.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const payload = JSON.parse(rawBody);
    const shop = payload.shop_domain;
    const customerId = payload.customer?.id?.toString();
    const customerEmail = payload.customer?.email;
    const ordersToRedact = payload.orders_to_redact || [];
    
    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Handle data deletion
    await handleDataDeletion(shop, customerId);
    
    // Log data deletion for compliance tracking
    console.log(`GDPR data redaction for shop: ${shop}`, {
      timestamp: new Date().toISOString(),
      shop,
      customerId,
      customerEmail,
      ordersToRedact: ordersToRedact.length,
      requestId: payload.data_request?.id,
    });

    // In a real implementation, you would:
    // 1. Delete or anonymize all customer data from your systems
    // 2. Remove customer references from bundle analytics
    // 3. Clean up any cached data
    // 4. Update audit logs to reflect the deletion
    // 5. Ensure compliance with data retention policies

    return json({ 
      success: true,
      message: "Customer data redacted successfully"
    }, { status: 200 });
  } catch (error) {
    console.error("Error handling customer data redaction:", error);
    return new Response("Internal server error", { status: 500 });
  }
}