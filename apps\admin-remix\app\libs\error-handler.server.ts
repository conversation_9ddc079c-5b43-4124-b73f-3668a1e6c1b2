import { json } from "@remix-run/node";
import { z } from "zod";
import { formatZodError } from "./validation.server";

export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = "AppError";
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public errors?: Record<string, string>) {
    super(message, 400, "VALIDATION_ERROR");
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = "Authentication required") {
    super(message, 401, "AUTHENTICATION_ERROR");
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = "Insufficient permissions") {
    super(message, 403, "AUTHORIZATION_ERROR");
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = "Resource not found") {
    super(message, 404, "NOT_FOUND");
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = "Rate limit exceeded") {
    super(message, 429, "RATE_LIMIT_EXCEEDED");
  }
}

export function handleError(error: unknown) {
  console.error("Error:", error);

  // Handle Zod validation errors
  if (error instanceof z.ZodError) {
    return json(
      {
        error: "Validation failed",
        details: formatZodError(error),
        code: "VALIDATION_ERROR",
      },
      { status: 400 }
    );
  }

  // Handle custom app errors
  if (error instanceof AppError) {
    return json(
      {
        error: error.message,
        code: error.code,
        ...(error instanceof ValidationError && error.errors && { errors: error.errors }),
      },
      { status: error.statusCode }
    );
  }

  // Handle Shopify API errors
  if (error && typeof error === "object" && "response" in error) {
    const shopifyError = error as any;
    const status = shopifyError.response?.status || 500;
    const message = shopifyError.response?.errors?.[0]?.message || "Shopify API error";
    
    return json(
      {
        error: message,
        code: "SHOPIFY_API_ERROR",
      },
      { status }
    );
  }

  // Handle standard errors
  if (error instanceof Error) {
    return json(
      {
        error: error.message,
        code: "INTERNAL_ERROR",
      },
      { status: 500 }
    );
  }

  // Fallback for unknown errors
  return json(
    {
      error: "An unexpected error occurred",
      code: "UNKNOWN_ERROR",
    },
    { status: 500 }
  );
}

// Retry helper for API calls
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxRetries?: number;
    delay?: number;
    backoff?: number;
    shouldRetry?: (error: unknown) => boolean;
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    delay = 1000,
    backoff = 2,
    shouldRetry = (error) => {
      // Retry on network errors and 5xx status codes
      if (error && typeof error === "object" && "response" in error) {
        const status = (error as any).response?.status;
        return status >= 500 && status < 600;
      }
      return false;
    },
  } = options;

  let lastError: unknown;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries - 1 || !shouldRetry(error)) {
        throw error;
      }
      
      // Wait before retrying
      const waitTime = delay * Math.pow(backoff, attempt);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
}