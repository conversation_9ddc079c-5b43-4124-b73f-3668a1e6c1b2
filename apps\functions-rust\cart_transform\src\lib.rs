use shopify_function::prelude::*;
use shopify_function::Result;
use bundleforge_shared::{BundleConfig, BundleMatch, parse_metafields, match_bundles};
use std::collections::HashSet;

generate_types!(
    query_path = "./input.graphql",
    schema_path = "./schema.graphql"
);

// Implement trait wrappers for generated types
impl bundleforge_shared::MetafieldConnection for input::MetafieldConnection {
    type Edge = input::MetafieldEdge;
    
    fn edges(&self) -> Option<&Vec<Self::Edge>> {
        self.edges.as_ref()
    }
}

impl bundleforge_shared::MetafieldEdge for input::MetafieldEdge {
    type Node = input::Metafield;
    
    fn node(&self) -> Option<&Self::Node> {
        self.node.as_ref()
    }
}

impl bundleforge_shared::MetafieldNode for input::Metafield {
    fn namespace(&self) -> &str {
        &self.namespace
    }
    
    fn key(&self) -> &str {
        &self.key
    }
    
    fn value(&self) -> Option<&str> {
        self.value.as_deref()
    }
}

impl bundleforge_shared::Cart for input::Cart {
    type Line = input::CartLine;
    
    fn lines(&self) -> Option<&Vec<Self::Line>> {
        self.lines.as_ref()
    }
}

impl bundleforge_shared::CartLine for input::CartLine {
    type Merchandise = input::ProductVariant;
    
    fn id(&self) -> &str {
        &self.id
    }
    
    fn quantity(&self) -> i64 {
        self.quantity
    }
    
    fn merchandise(&self) -> Option<&Self::Merchandise> {
        match &self.merchandise {
            input::Merchandise::ProductVariant(variant) => Some(variant),
            _ => None,
        }
    }
}

impl bundleforge_shared::Merchandise for input::ProductVariant {
    fn product_id(&self) -> &str {
        &self.product.id
    }
    
    fn variant_id(&self) -> &str {
        &self.id
    }
}

#[shopify_function_target]
fn function(input: input::ResponseData) -> Result<output::FunctionResult> {
    // Add panic handler for production safety
    std::panic::set_hook(Box::new(|_| {
        eprintln!("Cart transform function panicked, returning no-op");
    }));
    
    let result = match process_cart(&input.cart) {
        Ok(operations) => output::FunctionResult {
            operations,
        },
        Err(e) => {
            eprintln!("Error processing cart: {}", e);
            output::FunctionResult {
                operations: vec![],
            }
        }
    };
    
    Ok(result)
}

fn process_cart(cart: &input::Cart) -> Result<Vec<output::CartOperation>> {
    // Extract bundle configurations from cart metafields
    let bundle_configs = if let Some(metafields) = &cart.metafields {
        parse_metafields(metafields)
    } else {
        vec![]
    };
    
    if bundle_configs.is_empty() {
        return Ok(vec![]);
    }
    
    // Analyze cart for bundle opportunities
    let bundle_matches = match_bundles(cart, &bundle_configs);
    
    // Generate cart operations for bundle suggestions
    let operations = generate_cart_operations(&bundle_matches);
    
    // Check payload size limit (16KB)
    let payload_size = serde_json::to_string(&operations)?.len();
    if payload_size > 16 * 1024 {
        eprintln!("Cart operations payload exceeds 16KB limit: {} bytes", payload_size);
        return Ok(vec![]); // Return no-op if too large
    }
    
    Ok(operations)
}

fn generate_cart_operations(bundle_matches: &[BundleMatch]) -> Vec<output::CartOperation> {
    let mut operations = Vec::new();
    
    for bundle_match in bundle_matches {
        if !bundle_match.is_complete && !bundle_match.missing_products.is_empty() {
            // Suggest missing products to complete the bundle
            let message = format!(
                "Complete your {} bundle and save {}%! Missing items:",
                bundle_match.bundle_name,
                bundle_match.discount_percentage
            );
            
            // Create suggestion operations for missing products
            for missing_product in &bundle_match.missing_products {
                if let Some(operation) = create_suggestion_operation(
                    &message,
                    &missing_product.product_id,
                    missing_product.quantity,
                ) {
                    operations.push(operation);
                }
            }
        }
    }
    
    // Limit operations to prevent overwhelming the customer
    operations.truncate(3);
    
    operations
}

fn create_suggestion_operation(
    message: &str,
    product_id: &str,
    quantity: u32,
) -> Option<output::CartOperation> {
    // For now, return an empty operation as cart transform
    // In production, this would create proper suggestion UI
    None
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_empty_cart() {
        // Test implementation
    }
    
    #[test]
    fn test_bundle_matching() {
        // Test implementation
    }
}