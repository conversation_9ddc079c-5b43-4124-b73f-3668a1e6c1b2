import type { MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Card,
  DataTable,
  Badge,
  Button,
  Text,
} from "@shopify/polaris";
import { PlusIcon, EditIcon } from "@shopify/polaris-icons";
import { authenticatedLoader } from "../libs/auth.middleware.server";
import { BundleService } from "../libs/bundle.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Bundles - BundleForge" },
    { name: "description", content: "Manage your product bundles" },
  ];
};

export const loader = authenticatedLoader(async ({ request, auth }) => {
  try {
    const url = new URL(request.url);
    const after = url.searchParams.get("after") || undefined;
    const bundleService = new BundleService(auth.session.shop, auth.session.accessToken);

    // Get bundles with pagination
    const { bundles: rawBundles, pageInfo } = await bundleService.getBundlesPaginated({
      first: 20,
      after,
    });

    const bundles = rawBundles.map(bundle => ({
      id: bundle.id,
      name: bundle.name,
      status: bundle.active ? "active" : "draft",
      products: bundle.products.length,
      discount: `${bundle.discount_percentage}%`,
      created: bundle.created_at ? new Date(bundle.created_at).toLocaleDateString() : "Unknown",
    }));

    return json({ bundles, pageInfo });
  } catch (error) {
    console.error("Failed to load bundles:", error);
    return json({ bundles: [], pageInfo: { hasNextPage: false, endCursor: null } });
  }
});

export default function BundlesIndex() {
  const { bundles } = useLoaderData<typeof loader>();

  const rows = bundles.map((bundle) => [
    <Link to={`/bundles/${bundle.id}`} key={bundle.id}>
      <Text variant="bodyMd" as="span">
        {bundle.name}
      </Text>
    </Link>,
    <Badge
      key={`status-${bundle.id}`}
      tone={bundle.status === "active" ? "success" : "info"}
    >
      {bundle.status}
    </Badge>,
    bundle.products,
    bundle.discount,
    bundle.created,
    <Button
      key={`edit-${bundle.id}`}
      size="micro"
      icon={EditIcon}
      url={`/bundles/${bundle.id}/edit`}
      accessibilityLabel={`Edit ${bundle.name}`}
    />,
  ]);

  return (
    <Page
      title="Bundles"
      primaryAction={{
        content: "Create Bundle",
        icon: PlusIcon,
        url: "/bundles/new",
      }}
    >
      <Card>
        <DataTable
          columnContentTypes={[
            "text",
            "text",
            "numeric",
            "text",
            "text",
            "text",
          ]}
          headings={[
            "Name",
            "Status",
            "Products",
            "Discount",
            "Created",
            "Actions",
          ]}
          rows={rows}
        />
      </Card>
    </Page>
  );
}