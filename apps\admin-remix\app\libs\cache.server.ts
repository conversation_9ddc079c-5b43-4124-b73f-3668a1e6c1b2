import { createClient } from 'redis';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  namespace?: string;
}

class CacheService {
  private redisClient: ReturnType<typeof createClient> | null = null;
  private memoryCache = new Map<string, { value: any; expires: number }>();
  private useRedis: boolean;

  constructor() {
    this.useRedis = !!process.env.REDIS_URL && process.env.NODE_ENV === 'production';
  }

  private async getRedisClient() {
    if (!this.redisClient && this.useRedis) {
      this.redisClient = createClient({
        url: process.env.REDIS_URL,
        socket: {
          connectTimeout: 5000,
          reconnectStrategy: (retries) => {
            if (retries > 3) {
              console.error('Redis cache connection failed, falling back to memory cache');
              this.useRedis = false;
              return new Error('Redis connection failed');
            }
            return Math.min(retries * 100, 1000);
          }
        }
      });

      this.redisClient.on('error', (err) => {
        console.error('Redis Cache Error:', err);
        this.useRedis = false;
      });

      try {
        await this.redisClient.connect();
      } catch (error) {
        console.error('Failed to connect to Redis cache:', error);
        this.useRedis = false;
      }
    }
    
    return this.redisClient;
  }

  private getKey(key: string, namespace?: string): string {
    return namespace ? `${namespace}:${key}` : key;
  }

  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const fullKey = this.getKey(key, options.namespace);

    if (this.useRedis) {
      try {
        const client = await this.getRedisClient();
        if (client) {
          const value = await client.get(fullKey);
          if (value) {
            return JSON.parse(value);
          }
        }
      } catch (error) {
        console.error('Redis get error:', error);
      }
    }

    // Fallback to memory cache
    const cached = this.memoryCache.get(fullKey);
    if (cached && cached.expires > Date.now()) {
      return cached.value;
    }
    
    // Clean up expired entry
    if (cached) {
      this.memoryCache.delete(fullKey);
    }
    
    return null;
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const fullKey = this.getKey(key, options.namespace);
    const ttl = options.ttl || 300; // Default 5 minutes

    if (this.useRedis) {
      try {
        const client = await this.getRedisClient();
        if (client) {
          await client.setEx(fullKey, ttl, JSON.stringify(value));
          return;
        }
      } catch (error) {
        console.error('Redis set error:', error);
      }
    }

    // Fallback to memory cache
    this.memoryCache.set(fullKey, {
      value,
      expires: Date.now() + (ttl * 1000)
    });

    // Clean up old entries periodically
    if (this.memoryCache.size > 1000) {
      this.cleanup();
    }
  }

  async delete(key: string, options: CacheOptions = {}): Promise<void> {
    const fullKey = this.getKey(key, options.namespace);

    if (this.useRedis) {
      try {
        const client = await this.getRedisClient();
        if (client) {
          await client.del(fullKey);
        }
      } catch (error) {
        console.error('Redis delete error:', error);
      }
    }

    this.memoryCache.delete(fullKey);
  }

  async invalidatePattern(pattern: string, namespace?: string): Promise<void> {
    const fullPattern = this.getKey(pattern, namespace);

    if (this.useRedis) {
      try {
        const client = await this.getRedisClient();
        if (client) {
          const keys = await client.keys(fullPattern);
          if (keys.length > 0) {
            await client.del(keys);
          }
        }
      } catch (error) {
        console.error('Redis pattern delete error:', error);
      }
    }

    // For memory cache, iterate and delete matching keys
    const regex = new RegExp(fullPattern.replace('*', '.*'));
    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        this.memoryCache.delete(key);
      }
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, cached] of this.memoryCache.entries()) {
      if (cached.expires < now) {
        this.memoryCache.delete(key);
      }
    }
  }

  // Cache wrapper function for easy use
  async withCache<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    const data = await fetcher();
    
    // Store in cache
    await this.set(key, data, options);
    
    return data;
  }
}

// Export singleton instance
export const cache = new CacheService();

// Cache TTL constants
export const CACHE_TTL = {
  SHORT: 60,        // 1 minute
  MEDIUM: 300,      // 5 minutes
  LONG: 1800,       // 30 minutes
  HOUR: 3600,       // 1 hour
  DAY: 86400,       // 24 hours
};