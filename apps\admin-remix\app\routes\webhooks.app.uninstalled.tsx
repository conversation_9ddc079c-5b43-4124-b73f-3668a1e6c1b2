import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook, handleAppUninstall } from "../libs/auth.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const payload = JSON.parse(rawBody);
    const shop = payload.domain || payload.myshopify_domain;
    
    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Handle app uninstallation
    await handleAppUninstall(shop);
    
    // Log uninstallation for analytics without sensitive data
    console.log('App uninstalled', {
      timestamp: new Date().toISOString(),
      // Don't log shop domain or payload
    });

    return json({ success: true }, { status: 200 });
  } catch (error) {
    console.error("Error handling app uninstallation:", error);
    return new Response("Internal server error", { status: 500 });
  }
}