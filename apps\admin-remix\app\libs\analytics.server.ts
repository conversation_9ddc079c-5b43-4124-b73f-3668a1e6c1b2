import { z } from 'zod';
import { MetafieldService } from './metafield.server';
import type { Session } from '@shopify/shopify-api';
import { cache } from './cache.server';
import crypto from 'crypto';

// A/B Test Types
export interface ABTest {
  id: string;
  name: string;
  bundle_id: string;
  bundle_name?: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  traffic_allocation: number; // Percentage for variant A (control)
  variant_a: {
    name: string;
    discount_percentage: number;
  };
  variant_b: {
    name: string;
    discount_percentage: number;
  };
  success_metric: 'conversion_rate' | 'revenue' | 'aov';
  minimum_sample_size: number;
  auto_end: boolean;
  participants: number;
  results?: ABTestResults;
  created_at: string;
  started_at?: string;
  ended_at?: string;
  shop_domain: string;
}

export interface ABTestResults {
  variant_a_views: number;
  variant_a_conversions: number;
  variant_a_revenue: number;
  variant_b_views: number;
  variant_b_conversions: number;
  variant_b_revenue: number;
  statistical_significance: number;
  confidence_interval: [number, number];
  lift: number;
  winner?: 'variant_a' | 'variant_b' | 'no_winner';
  recommendation?: string;
}

export interface VariantAssignment {
  test_id: string;
  variant: 'variant_a' | 'variant_b';
  customer_id: string;
  assigned_at: string;
}

// Analytics Event Types
export interface AnalyticsEvent {
  id: string;
  event_type: 'bundle_view' | 'bundle_add_to_cart' | 'bundle_purchase' | 'ab_test_exposure';
  bundle_id: string;
  customer_id?: string;
  session_id: string;
  ab_test_id?: string;
  ab_test_variant?: 'variant_a' | 'variant_b';
  revenue?: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

const AB_TEST_NAMESPACE = 'bundleforge_ab_tests';
const ANALYTICS_NAMESPACE = 'bundleforge_analytics';
const VARIANT_ASSIGNMENT_NAMESPACE = 'bundleforge_variant_assignments';

export class AnalyticsService {
  private metafieldService: MetafieldService;
  private shop: string;
  
  constructor(shop: string, graphql?: any) {
    this.shop = shop;
    this.metafieldService = new MetafieldService(shop, graphql);
  }
  
  // A/B Test Management
  async createABTest(data: Omit<ABTest, 'id' | 'created_at' | 'participants' | 'shop_domain' | 'status'>): Promise<ABTest> {
    const abTest: ABTest = {
      ...data,
      id: `ab_test_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`,
      status: 'draft',
      participants: 0,
      created_at: new Date().toISOString(),
      shop_domain: this.shop,
    };
    
    await this.saveABTest(abTest);
    await cache.invalidatePattern(`analytics:ab_tests:${this.shop}`);
    
    return abTest;
  }
  
  async getABTests(): Promise<ABTest[]> {
    return cache.withCache(
      `analytics:ab_tests:${this.shop}`,
      async () => {
        const metafields = await this.metafieldService.getMetafieldsByNamespace(AB_TEST_NAMESPACE);
        return metafields
          .map(mf => {
            try {
              return JSON.parse(mf.value) as ABTest;
            } catch {
              return null;
            }
          })
          .filter(Boolean)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      },
      300 // 5 minute cache
    );
  }
  
  async getABTestById(testId: string): Promise<ABTest | null> {
    const tests = await this.getABTests();
    return tests.find(test => test.id === testId) || null;
  }
  
  async updateABTestStatus(testId: string, status: ABTest['status'], results?: ABTestResults): Promise<void> {
    const test = await this.getABTestById(testId);
    if (!test) throw new Error('A/B test not found');
    
    test.status = status;
    
    if (status === 'active' && !test.started_at) {
      test.started_at = new Date().toISOString();
    } else if (status === 'completed') {
      test.ended_at = new Date().toISOString();
      if (results) {
        test.results = results;
      }
    }
    
    await this.saveABTest(test);
    await cache.invalidatePattern(`analytics:ab_tests:${this.shop}`);
  }
  
  async deleteABTest(testId: string): Promise<void> {
    await this.metafieldService.deleteMetafield(
      AB_TEST_NAMESPACE,
      `ab_test_${testId}`
    );
    await cache.invalidatePattern(`analytics:ab_tests:${this.shop}`);
  }
  
  // Variant Assignment (Hash-based for consistency)
  async getVariantAssignment(customerId: string, testId: string): Promise<'variant_a' | 'variant_b'> {
    const cacheKey = `variant:${this.shop}:${testId}:${customerId}`;
    
    return cache.withCache(
      cacheKey,
      async () => {
        // Check if already assigned
        const existingAssignment = await this.getExistingAssignment(customerId, testId);
        if (existingAssignment) {
          return existingAssignment.variant;
        }
        
        // Get test configuration
        const test = await this.getABTestById(testId);
        if (!test || test.status !== 'active') {
          return 'variant_a'; // Default to control if test not active
        }
        
        // Hash-based assignment for consistency
        const hash = crypto
          .createHash('md5')
          .update(`${customerId}:${testId}`)
          .digest('hex');
        
        const hashValue = parseInt(hash.substring(0, 8), 16);
        const threshold = (test.traffic_allocation / 100) * 0xffffffff;
        
        const variant = hashValue <= threshold ? 'variant_a' : 'variant_b';
        
        // Save assignment
        await this.saveVariantAssignment({
          test_id: testId,
          variant,
          customer_id: customerId,
          assigned_at: new Date().toISOString(),
        });
        
        // Increment participant count
        test.participants = (test.participants || 0) + 1;
        await this.saveABTest(test);
        
        return variant;
      },
      86400 // 24 hour cache
    );
  }
  
  private async getExistingAssignment(customerId: string, testId: string): Promise<VariantAssignment | null> {
    const key = `assignment_${customerId}_${testId}`;
    const metafield = await this.metafieldService.getMetafield(
      VARIANT_ASSIGNMENT_NAMESPACE,
      key
    );
    
    if (metafield) {
      try {
        return JSON.parse(metafield.value) as VariantAssignment;
      } catch {
        return null;
      }
    }
    
    return null;
  }
  
  private async saveVariantAssignment(assignment: VariantAssignment): Promise<void> {
    const key = `assignment_${assignment.customer_id}_${assignment.test_id}`;
    await this.metafieldService.setMetafield(
      VARIANT_ASSIGNMENT_NAMESPACE,
      key,
      JSON.stringify(assignment),
      'json'
    );
  }
  
  // Analytics Event Tracking
  async trackEvent(event: Omit<AnalyticsEvent, 'id' | 'timestamp'>): Promise<void> {
    const analyticsEvent: AnalyticsEvent = {
      ...event,
      id: `event_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`,
      timestamp: new Date().toISOString(),
    };
    
    // Save event
    await this.saveAnalyticsEvent(analyticsEvent);
    
    // Update A/B test metrics if applicable
    if (event.ab_test_id && event.ab_test_variant) {
      await this.updateABTestMetrics(event.ab_test_id, event.ab_test_variant, event.event_type, event.revenue);
    }
    
    // Invalidate relevant caches
    await cache.invalidatePattern(`analytics:events:${this.shop}:${event.bundle_id}`);
  }
  
  private async saveAnalyticsEvent(event: AnalyticsEvent): Promise<void> {
    const dayKey = new Date(event.timestamp).toISOString().split('T')[0];
    const key = `events_${event.bundle_id}_${dayKey}`;
    
    // Get existing events for the day
    const existingData = await this.metafieldService.getMetafield(
      ANALYTICS_NAMESPACE,
      key
    );
    
    let events: AnalyticsEvent[] = [];
    if (existingData) {
      try {
        events = JSON.parse(existingData.value);
      } catch {
        events = [];
      }
    }
    
    events.push(event);
    
    // Keep only last 1000 events per day
    if (events.length > 1000) {
      events = events.slice(-1000);
    }
    
    await this.metafieldService.setMetafield(
      ANALYTICS_NAMESPACE,
      key,
      JSON.stringify(events),
      'json'
    );
  }
  
  // A/B Test Results Calculation
  async calculateABTestResults(testId: string): Promise<ABTestResults> {
    const test = await this.getABTestById(testId);
    if (!test) throw new Error('A/B test not found');
    
    // Get all events for this test
    const events = await this.getABTestEvents(testId);
    
    // Calculate metrics for each variant
    const variantAEvents = events.filter(e => e.ab_test_variant === 'variant_a');
    const variantBEvents = events.filter(e => e.ab_test_variant === 'variant_b');
    
    const results: ABTestResults = {
      variant_a_views: variantAEvents.filter(e => e.event_type === 'bundle_view').length,
      variant_a_conversions: variantAEvents.filter(e => e.event_type === 'bundle_purchase').length,
      variant_a_revenue: variantAEvents
        .filter(e => e.event_type === 'bundle_purchase')
        .reduce((sum, e) => sum + (e.revenue || 0), 0),
      variant_b_views: variantBEvents.filter(e => e.event_type === 'bundle_view').length,
      variant_b_conversions: variantBEvents.filter(e => e.event_type === 'bundle_purchase').length,
      variant_b_revenue: variantBEvents
        .filter(e => e.event_type === 'bundle_purchase')
        .reduce((sum, e) => sum + (e.revenue || 0), 0),
      statistical_significance: 0,
      confidence_interval: [0, 0],
      lift: 0,
    };
    
    // Calculate conversion rates
    const variantAConversionRate = results.variant_a_views > 0 
      ? results.variant_a_conversions / results.variant_a_views 
      : 0;
    const variantBConversionRate = results.variant_b_views > 0 
      ? results.variant_b_conversions / results.variant_b_views 
      : 0;
    
    // Calculate statistical significance using z-test for proportions
    const pooledConversionRate = (results.variant_a_conversions + results.variant_b_conversions) / 
      (results.variant_a_views + results.variant_b_views);
    
    const standardError = Math.sqrt(
      pooledConversionRate * (1 - pooledConversionRate) * 
      (1 / results.variant_a_views + 1 / results.variant_b_views)
    );
    
    const zScore = standardError > 0 
      ? Math.abs(variantBConversionRate - variantAConversionRate) / standardError 
      : 0;
    
    // Convert z-score to confidence level (two-tailed test)
    results.statistical_significance = this.zScoreToConfidence(zScore) * 100;
    
    // Calculate lift
    if (variantAConversionRate > 0) {
      results.lift = ((variantBConversionRate - variantAConversionRate) / variantAConversionRate) * 100;
    }
    
    // Calculate confidence interval for the difference
    const marginOfError = 1.96 * standardError; // 95% confidence
    results.confidence_interval = [
      (variantBConversionRate - variantAConversionRate) - marginOfError,
      (variantBConversionRate - variantAConversionRate) + marginOfError,
    ];
    
    // Determine winner
    if (results.statistical_significance >= 95) {
      if (variantBConversionRate > variantAConversionRate) {
        results.winner = 'variant_b';
        results.recommendation = `The variant "${test.variant_b.name}" shows a ${results.lift.toFixed(1)}% improvement over the control. Consider implementing this discount permanently.`;
      } else if (variantAConversionRate > variantBConversionRate) {
        results.winner = 'variant_a';
        results.recommendation = `The control "${test.variant_a.name}" performs better than the variant. Keep the current discount structure.`;
      }
    } else {
      results.winner = 'no_winner';
      results.recommendation = `The test has not reached statistical significance (${results.statistical_significance.toFixed(1)}%). Continue running the test to gather more data.`;
    }
    
    return results;
  }
  
  private async getABTestEvents(testId: string): Promise<AnalyticsEvent[]> {
    const metafields = await this.metafieldService.getMetafieldsByNamespace(ANALYTICS_NAMESPACE);
    const events: AnalyticsEvent[] = [];
    
    for (const metafield of metafields) {
      try {
        const dayEvents = JSON.parse(metafield.value) as AnalyticsEvent[];
        const testEvents = dayEvents.filter(e => e.ab_test_id === testId);
        events.push(...testEvents);
      } catch {
        // Skip invalid data
      }
    }
    
    return events;
  }
  
  private async updateABTestMetrics(
    testId: string,
    variant: 'variant_a' | 'variant_b',
    eventType: string,
    revenue?: number
  ): Promise<void> {
    // In a production system, you might want to update these metrics in real-time
    // For now, we calculate them on demand in calculateABTestResults
    await cache.invalidatePattern(`analytics:ab_test_results:${this.shop}:${testId}`);
  }
  
  private zScoreToConfidence(zScore: number): number {
    // Approximate conversion of z-score to confidence level
    // Using a simplified normal distribution approximation
    if (zScore >= 2.576) return 0.99;   // 99% confidence
    if (zScore >= 1.96) return 0.95;    // 95% confidence
    if (zScore >= 1.645) return 0.90;   // 90% confidence
    if (zScore >= 1.282) return 0.80;   // 80% confidence
    return zScore / 2.576;               // Linear approximation below 80%
  }
  
  private async saveABTest(test: ABTest): Promise<void> {
    await this.metafieldService.setMetafield(
      AB_TEST_NAMESPACE,
      `ab_test_${test.id}`,
      JSON.stringify(test),
      'json'
    );
  }
  
  // Bundle Analytics
  async getBundleAnalytics(bundleId: string, days: number = 30): Promise<any> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const events = await this.getBundleEvents(bundleId, startDate, endDate);
    
    // Calculate metrics
    const views = events.filter(e => e.event_type === 'bundle_view').length;
    const addToCarts = events.filter(e => e.event_type === 'bundle_add_to_cart').length;
    const purchases = events.filter(e => e.event_type === 'bundle_purchase').length;
    const revenue = events
      .filter(e => e.event_type === 'bundle_purchase')
      .reduce((sum, e) => sum + (e.revenue || 0), 0);
    
    return {
      bundle_id: bundleId,
      period: { start: startDate.toISOString(), end: endDate.toISOString() },
      metrics: {
        views,
        add_to_carts: addToCarts,
        purchases,
        revenue,
        conversion_rate: views > 0 ? (purchases / views) * 100 : 0,
        cart_abandonment_rate: addToCarts > 0 ? ((addToCarts - purchases) / addToCarts) * 100 : 0,
        average_order_value: purchases > 0 ? revenue / purchases : 0,
      },
      daily_breakdown: this.getDailyBreakdown(events, startDate, endDate),
    };
  }
  
  private async getBundleEvents(
    bundleId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AnalyticsEvent[]> {
    const events: AnalyticsEvent[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dayKey = currentDate.toISOString().split('T')[0];
      const key = `events_${bundleId}_${dayKey}`;
      
      const data = await this.metafieldService.getMetafield(ANALYTICS_NAMESPACE, key);
      if (data) {
        try {
          const dayEvents = JSON.parse(data.value) as AnalyticsEvent[];
          events.push(...dayEvents);
        } catch {
          // Skip invalid data
        }
      }
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return events;
  }
  
  private getDailyBreakdown(events: AnalyticsEvent[], startDate: Date, endDate: Date): any[] {
    const breakdown: any[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dayStart = new Date(currentDate);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(currentDate);
      dayEnd.setHours(23, 59, 59, 999);
      
      const dayEvents = events.filter(e => {
        const eventDate = new Date(e.timestamp);
        return eventDate >= dayStart && eventDate <= dayEnd;
      });
      
      breakdown.push({
        date: currentDate.toISOString().split('T')[0],
        views: dayEvents.filter(e => e.event_type === 'bundle_view').length,
        add_to_carts: dayEvents.filter(e => e.event_type === 'bundle_add_to_cart').length,
        purchases: dayEvents.filter(e => e.event_type === 'bundle_purchase').length,
        revenue: dayEvents
          .filter(e => e.event_type === 'bundle_purchase')
          .reduce((sum, e) => sum + (e.revenue || 0), 0),
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return breakdown;
  }
}