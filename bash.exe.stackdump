Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF4710) msys-2.0.dll+0x1FEBA
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210285FF9, 0007FFFF56C8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF5810  0002100690B4 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF5AF0  00021006A49D (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFACB880000 ntdll.dll
7FFACA090000 KERNEL32.DLL
7FFAC8EB0000 KERNELBASE.dll
7FFAC9EC0000 USER32.dll
7FFAC9470000 win32u.dll
7FFACA580000 GDI32.dll
7FFAC9330000 gdi32full.dll
7FFAC8CB0000 msvcp_win.dll
7FFAC8D60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFACA490000 advapi32.dll
7FFAC9E00000 msvcrt.dll
7FFACA160000 sechost.dll
7FFACA5B0000 RPCRT4.dll
7FFAC7EE0000 CRYPTBASE.DLL
7FFAC89D0000 bcryptPrimitives.dll
7FFACB0B0000 IMM32.DLL
