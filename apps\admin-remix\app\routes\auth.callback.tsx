import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import {
  exchangeCodeForToken,
  verifyHmac,
  verifyState,
  saveSession,
  generateSessionId,
  type ShopifySession,
} from "~/libs/auth.server";
import { getAppUrl } from "~/libs/app-url.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const searchParams = Object.fromEntries(url.searchParams.entries());
  
  const {
    code,
    hmac,
    shop,
    state,
    timestamp,
  } = searchParams;

  // Verify required parameters
  if (!code || !hmac || !shop || !state) {
    throw new Response("Missing required parameters", { status: 400 });
  }

  // Verify HMAC
  if (!verifyHmac(searchParams, hmac)) {
    throw new Response("Invalid HMAC", { status: 401 });
  }

  // Verify timestamp (prevent replay attacks)
  if (timestamp) {
    const requestTime = parseInt(timestamp, 10) * 1000;
    const currentTime = Date.now();
    const timeDiff = currentTime - requestTime;
    
    // Reject requests older than 5 minutes
    if (timeDiff > 5 * 60 * 1000) {
      throw new Response("Request too old", { status: 401 });
    }
  }

  // Verify state parameter
  if (!verifyState(state, shop)) {
    throw new Response("Invalid state parameter", { status: 401 });
  }

  try {
    // Exchange code for access token
    const tokenData = await exchangeCodeForToken(shop, code);
    
    // Create session
    const session: ShopifySession = {
      shop,
      accessToken: tokenData.access_token,
      scope: tokenData.scope,
      isOnline: !!tokenData.associated_user,
      state,
    };

    // Handle online access token (user-specific)
    if (tokenData.associated_user) {
      session.onlineAccessInfo = {
        expires_in: tokenData.expires_in || 86400,
        associated_user_scope: tokenData.associated_user_scope || "",
        associated_user: tokenData.associated_user,
      };
      
      // Set expiration for online tokens
      if (tokenData.expires_in) {
        session.expires = new Date(Date.now() + tokenData.expires_in * 1000);
      }
    }

    // Generate session ID
    const sessionId = generateSessionId(
      shop,
      session.onlineAccessInfo?.associated_user?.id?.toString()
    );
    
    // Save session
    await saveSession(sessionId, session);

    // TODO: Store installation in database
    console.log(`App installed/authenticated for shop: ${shop}`);
    
    // Set up webhooks with dynamic URL
    const appUrl = getAppUrl(request);
    await setupWebhooks(shop, tokenData.access_token, appUrl);
    
    // TODO: Initialize default bundle configurations
    await initializeShopData(shop, tokenData.access_token);

    // Redirect to admin dashboard
    const redirectUrl = new URL("/", request.url);
    redirectUrl.searchParams.set("shop", shop);
    
    return redirect(redirectUrl.toString());
  } catch (error) {
    console.error("OAuth callback error:", error);
    throw new Response("Authentication failed", { status: 500 });
  }
}

// Setup required webhooks
async function setupWebhooks(shop: string, accessToken: string, appUrl: string) {
  const webhooks = [
    {
      topic: "app/uninstalled",
      address: `${appUrl}/webhooks/app/uninstalled`,
      format: "json",
    },
    {
      topic: "customers/data_request",
      address: `${appUrl}/webhooks/customers/data_request`,
      format: "json",
    },
    {
      topic: "customers/redact",
      address: `${appUrl}/webhooks/customers/redact`,
      format: "json",
    },
    {
      topic: "shop/redact",
      address: `${appUrl}/webhooks/shop/redact`,
      format: "json",
    },
    {
      topic: "orders/create",
      address: `${appUrl}/webhooks/orders/create`,
      format: "json",
    },
    {
      topic: "orders/updated",
      address: `${appUrl}/webhooks/orders/updated`,
      format: "json",
    },
    {
      topic: "products/create",
      address: `${appUrl}/webhooks/products/create`,
      format: "json",
    },
    {
      topic: "products/update",
      address: `${appUrl}/webhooks/products/update`,
      format: "json",
    },
  ];

  const createWebhookMutation = `
    mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
      webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
        webhookSubscription {
          id
          callbackUrl
          topic
          format
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  for (const webhook of webhooks) {
    try {
      const response = await fetch(`https://${shop}/admin/api/2024-01/graphql.json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": accessToken,
        },
        body: JSON.stringify({
          query: createWebhookMutation,
          variables: {
            topic: webhook.topic.toUpperCase().replace("/", "_"),
            webhookSubscription: {
              callbackUrl: webhook.address,
              format: webhook.format.toUpperCase(),
            },
          },
        }),
      });

      const result = await response.json();
      
      if (result.data?.webhookSubscriptionCreate?.userErrors?.length > 0) {
        console.error(`Failed to create webhook ${webhook.topic}:`, result.data.webhookSubscriptionCreate.userErrors);
      } else {
        console.log(`Created webhook: ${webhook.topic}`);
      }
    } catch (error) {
      console.error(`Error creating webhook ${webhook.topic}:`, error);
    }
  }
}

// Initialize shop-specific data
async function initializeShopData(shop: string, accessToken: string) {
  try {
    // TODO: Create default bundle configurations in database
    // TODO: Set up analytics tracking
    // TODO: Initialize A/B testing configurations
    
    console.log(`Initialized shop data for: ${shop}`);
  } catch (error) {
    console.error(`Error initializing shop data for ${shop}:`, error);
  }
}