import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook } from "../libs/auth.server";
import { ProductWebhookSchema } from "../libs/validation.server";
import { handleError } from "../libs/error-handler.server";
import { sessionStorage } from "../libs/session-storage.server";
import {
  findBundlesContainingProduct,
  createOrUpdateBundle,
  getAppPreferences,
  type BundleConfig
} from "../libs/metafield.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const productData = JSON.parse(rawBody);
    const topic = request.headers.get("x-shopify-topic");
    const shop = request.headers.get("x-shopify-shop-domain");

    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Validate product data
    const product = ProductWebhookSchema.parse(productData);

    // Route to appropriate product handler based on topic
    switch (topic) {
      case "products/create":
        return await handleProductCreate(product, shop);

      case "products/update":
        return await handleProductUpdate(product, shop);

      case "products/delete":
        return await handleProductDelete(product, shop);

      default:
        return new Response("Unknown product topic", { status: 400 });
    }
  } catch (error) {
    return handleError(error);
  }
}

async function handleProductCreate(product: any, shop: string) {
  // Check if this is a bundle product or if it should be added to existing bundles
  await syncProductWithBundles(product, shop, 'create');
  
  // Log product creation
  console.log(`Product created for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
    productType: product.product_type,
    vendor: product.vendor,
    status: product.status,
  });

  return json({ success: true, message: "Product creation processed" }, { status: 200 });
}

async function handleProductUpdate(product: any, shop: string) {
  // Sync product changes with bundle configurations
  await syncProductWithBundles(product, shop, 'update');
  
  // Check if product availability changed
  if (product.status === 'archived' || product.status === 'draft') {
    await handleProductUnavailable(product, shop);
  }
  
  // Log product update
  console.log(`Product updated for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
    status: product.status,
    updatedAt: product.updated_at,
  });

  return json({ success: true, message: "Product update processed" }, { status: 200 });
}

async function handleProductDelete(product: any, shop: string) {
  try {
    const session = await createSessionFromWebhook(shop);

    // Find all bundles containing this product
    const affectedBundles = await findBundlesContainingProduct(session, product.id.toString());

    // Remove product from each bundle or deactivate bundle if needed
    for (const bundle of affectedBundles) {
      const updatedBundle = { ...bundle };
      updatedBundle.products = updatedBundle.products.filter((p: any) => p.product_id !== product.id.toString());

      // If bundle has less than 2 products after removal, deactivate it
      if (updatedBundle.products.length < 2) {
        updatedBundle.active = false;
      }

      await createOrUpdateBundle(session, updatedBundle);
    }

    console.log(`Removed product ${product.id} from ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error(`Error processing product deletion for ${product.id}:`, error);
  }

  // Log product deletion
  console.log(`Product deleted for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
  });

  return json({ success: true, message: "Product deletion processed" }, { status: 200 });
}

// Sync product with bundle configurations
async function syncProductWithBundles(product: any, shop: string, action: 'create' | 'update'): Promise<void> {
  try {
    const session = await createSessionFromWebhook(shop);

    // Check if this product is part of any bundles
    const affectedBundles = await findBundlesContainingProduct(session, product.id.toString());

    if (affectedBundles.length === 0) {
      // For create action, check if product should be auto-added to bundles
      if (action === 'create') {
        await checkAutoCreateBundles(session, product, shop);
      }
      return; // Product is not part of any bundles
    }

    // Update bundle configurations
    for (const bundle of affectedBundles) {
      // Update product information in the bundle
      const updatedBundle = { ...bundle };
      const productIndex = updatedBundle.products.findIndex((p: any) => p.product_id === product.id.toString());

      if (productIndex >= 0) {
        // Update existing product info
        updatedBundle.products[productIndex] = {
          ...updatedBundle.products[productIndex],
          // Add any additional product metadata if needed
        };

        // If product is no longer available, mark bundle as inactive or remove product
        if (product.status === 'archived' || product.status === 'draft') {
          if (updatedBundle.products.length <= 2) {
            // If removing this product would leave less than 2 products, deactivate bundle
            updatedBundle.active = false;
          } else {
            // Remove the product from the bundle
            updatedBundle.products.splice(productIndex, 1);
          }
        }

        await createOrUpdateBundle(session, updatedBundle);
      }
    }

    console.log(`Synced product ${product.id} with ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error('Error syncing product with bundles:', error);
  }
}

// Handle product becoming unavailable
async function handleProductUnavailable(product: any, shop: string): Promise<void> {
  try {
    const session = await createSessionFromWebhook(shop);

    // Find bundles that contain this product
    const affectedBundles = await findBundlesContainingProduct(session, product.id.toString());

    if (affectedBundles.length === 0) {
      return;
    }

    // Disable or update bundles that contain unavailable products
    for (const bundle of affectedBundles) {
      const updatedBundle = { ...bundle };

      // Option 1: Disable the entire bundle if it contains unavailable products
      // This is the safest approach to avoid selling unavailable items
      updatedBundle.active = false;

      // Option 2: Remove the unavailable product (commented out)
      // updatedBundle.products = updatedBundle.products.filter((p: any) => p.product_id !== product.id.toString());
      // if (updatedBundle.products.length < 2) {
      //   updatedBundle.active = false;
      // }

      await createOrUpdateBundle(session, updatedBundle);

      console.log(`Disabled bundle ${bundle.id} due to unavailable product ${product.id}`);
    }

    console.log(`Handled unavailable product ${product.id} in ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error('Error handling unavailable product:', error);
  }
}

// Check if a new product should be automatically added to bundles
async function checkAutoCreateBundles(session: any, product: any, shop: string): Promise<void> {
  try {
    // Get app preferences to check if auto-create is enabled
    const preferences = await getAppPreferences(session);

    if (!preferences || !preferences.autoCreateBundles) {
      return; // Auto-create is disabled
    }

    // TODO: Implement auto-bundle logic based on:
    // - Product type
    // - Product vendor
    // - Product tags
    // - Product collections
    // - Price ranges
    // - Custom rules defined in preferences

    console.log(`Checking auto-create bundles for product ${product.id} in shop ${shop}`);

    // Example: Auto-create bundles for products with specific tags
    if (product.tags && product.tags.includes('bundle-eligible')) {
      // Find similar products to create a bundle with
      // This would involve querying Shopify for related products
      console.log(`Product ${product.id} is eligible for auto-bundling`);
    }
  } catch (error) {
    console.error('Error checking auto-create bundles:', error);
  }
}

// Create a session object for metafield operations
async function createSessionFromWebhook(shop: string): Promise<any> {
  try {
    // Try to get the offline session for the shop
    const sessionId = `offline_${shop}`;
    const session = await sessionStorage.get(sessionId);

    if (session) {
      return session;
    }

    // Fallback: create a minimal session object
    // In production, you should ensure all shops have valid sessions stored
    console.warn(`No stored session found for shop: ${shop}. Using fallback session.`);
    return {
      shop: shop,
      accessToken: '', // This would need to be retrieved from your app installation
      isOnline: false,
    };
  } catch (error) {
    console.error(`Error retrieving session for shop ${shop}:`, error);
    throw new Error(`Unable to retrieve session for shop: ${shop}`);
  }
}


