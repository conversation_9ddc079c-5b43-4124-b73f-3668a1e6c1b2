import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook } from "~/libs/auth.server";
import { ProductWebhookSchema } from "~/libs/validation.server";
import { handleError } from "~/libs/error-handler.server";
import {
  findBundlesContainingProduct,
  createOrUpdateBundle,
  getBundleFromProduct,
  saveBundleToProduct,
  type BundleConfig
} from "~/libs/metafield.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const productData = JSON.parse(rawBody);
    const topic = request.headers.get("x-shopify-topic");
    const shop = request.headers.get("x-shopify-shop-domain");

    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Validate product data
    const product = ProductWebhookSchema.parse(productData);

    // Route to appropriate product handler based on topic
    switch (topic) {
      case "products/create":
        return await handleProductCreate(product, shop);

      case "products/update":
        return await handleProductUpdate(product, shop);

      case "products/delete":
        return await handleProductDelete(product, shop);

      default:
        return new Response("Unknown product topic", { status: 400 });
    }
  } catch (error) {
    return handleError(error);
  }
}

async function handleProductCreate(product: any, shop: string) {
  // Check if this is a bundle product or if it should be added to existing bundles
  await syncProductWithBundles(product, shop, 'create');
  
  // Log product creation
  console.log(`Product created for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
    productType: product.product_type,
    vendor: product.vendor,
    status: product.status,
  });

  return json({ success: true, message: "Product creation processed" }, { status: 200 });
}

async function handleProductUpdate(product: any, shop: string) {
  // Sync product changes with bundle configurations
  await syncProductWithBundles(product, shop, 'update');
  
  // Check if product availability changed
  if (product.status === 'archived' || product.status === 'draft') {
    await handleProductUnavailable(product, shop);
  }
  
  // Log product update
  console.log(`Product updated for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
    status: product.status,
    updatedAt: product.updated_at,
  });

  return json({ success: true, message: "Product update processed" }, { status: 200 });
}

async function handleProductDelete(product: any, shop: string) {
  try {
    const session = createSessionFromWebhook(shop);

    // Find all bundles containing this product
    const affectedBundles = await findBundlesContainingProduct(session, product.id.toString());

    // Remove product from each bundle or deactivate bundle if needed
    for (const bundle of affectedBundles) {
      const updatedBundle = { ...bundle };
      updatedBundle.products = updatedBundle.products.filter((p: any) => p.product_id !== product.id.toString());

      // If bundle has less than 2 products after removal, deactivate it
      if (updatedBundle.products.length < 2) {
        updatedBundle.active = false;
      }

      await createOrUpdateBundle(session, updatedBundle);
    }

    console.log(`Removed product ${product.id} from ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error(`Error processing product deletion for ${product.id}:`, error);
  }

  // Log product deletion
  console.log(`Product deleted for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    productId: product.id,
    productTitle: product.title,
  });

  return json({ success: true, message: "Product deletion processed" }, { status: 200 });
}

// Sync product with bundle configurations
async function syncProductWithBundles(product: any, shop: string, action: 'create' | 'update'): Promise<void> {
  try {
    const session = createSessionFromWebhook(shop);

    // Check if this product is part of any bundles
    const affectedBundles = await findBundlesContainingProduct(session, product.id.toString());

    if (affectedBundles.length === 0) {
      return; // Product is not part of any bundles
    }

    // Update bundle configurations
    for (const bundle of affectedBundles) {
      // Update product information in the bundle
      const updatedBundle = { ...bundle };
      const productIndex = updatedBundle.products.findIndex((p: any) => p.product_id === product.id.toString());

      if (productIndex >= 0) {
        // Update existing product info
        updatedBundle.products[productIndex] = {
          ...updatedBundle.products[productIndex],
          // Add any additional product metadata if needed
        };

        // If product is no longer available, mark bundle as inactive or remove product
        if (product.status === 'archived' || product.status === 'draft') {
          if (updatedBundle.products.length <= 2) {
            // If removing this product would leave less than 2 products, deactivate bundle
            updatedBundle.active = false;
          } else {
            // Remove the product from the bundle
            updatedBundle.products.splice(productIndex, 1);
          }
        }

        await createOrUpdateBundle(session, updatedBundle);
      }
    }

    console.log(`Synced product ${product.id} with ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error('Error syncing product with bundles:', error);
  }
}

// Handle product becoming unavailable
async function handleProductUnavailable(product: any, shop: string): Promise<void> {
  try {
    // Find bundles that contain this product
    const affectedBundles = await findBundlesContainingProduct(product.id, shop);
    
    if (affectedBundles.length === 0) {
      return;
    }

    // Disable or update bundles that contain unavailable products
    for (const bundle of affectedBundles) {
      await handleBundleWithUnavailableProduct(bundle, product, shop);
    }
    
    console.log(`Handled unavailable product ${product.id} in ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error('Error handling unavailable product:', error);
  }
}

// Remove product from all bundles
async function removeProductFromBundles(productId: number, shop: string): Promise<void> {
  try {
    // Find all bundles containing this product
    const affectedBundles = await findBundlesContainingProduct(productId, shop);
    
    if (affectedBundles.length === 0) {
      return;
    }

    // Remove product from each bundle
    for (const bundle of affectedBundles) {
      await removeProductFromBundle(bundle.id, productId, shop);
    }
    
    console.log(`Removed product ${productId} from ${affectedBundles.length} bundles`);
  } catch (error) {
    console.error('Error removing product from bundles:', error);
  }
}

// Create a session object for metafield operations
function createSessionFromWebhook(shop: string): any {
  // Note: In a real webhook, you'd need to get the access token for the shop
  // This is a simplified version - in production, you'd retrieve the stored session
  return {
    shop: shop,
    accessToken: process.env.SHOPIFY_ACCESS_TOKEN || '', // This should be retrieved from your session storage
  };
}

// Update bundle product information
async function updateBundleProductInfo(bundleId: string, product: any, shop: string): Promise<void> {
  // TODO: Implement bundle product info update
  // This would update the bundle metafields with new product information
  
  console.log(`Updating bundle ${bundleId} with product info for ${product.id}`);
}

// Recalculate bundle pricing
async function recalculateBundlePricing(bundles: any[], shop: string): Promise<void> {
  // TODO: Implement bundle pricing recalculation
  // This would recalculate bundle prices based on updated product prices
  
  console.log(`Recalculating pricing for ${bundles.length} bundles in shop ${shop}`);
}

// Handle bundle with unavailable product
async function handleBundleWithUnavailableProduct(bundle: any, product: any, shop: string): Promise<void> {
  // TODO: Implement bundle handling for unavailable products
  // Options:
  // 1. Disable the entire bundle
  // 2. Remove the unavailable product from the bundle
  // 3. Mark the bundle as "partially available"
  // 4. Suggest alternative products
  
  console.log(`Handling bundle ${bundle.id} with unavailable product ${product.id}`);
}

// Remove product from a specific bundle
async function removeProductFromBundle(bundleId: string, productId: number, shop: string): Promise<void> {
  // TODO: Implement product removal from bundle
  // This would update the bundle metafields to remove the product
  
  console.log(`Removing product ${productId} from bundle ${bundleId} in shop ${shop}`);
}
