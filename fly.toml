# Fly.io deployment configuration for BundleForge

app = "bundleforge"
primary_region = "iad"
kill_signal = "SIGINT"
kill_timeout = "5s"

[experimental]
  auto_rollback = true

[build]
  dockerfile = "Dockerfile"

[env]
  NODE_ENV = "production"
  PORT = "3000"
  # Add other non-secret environment variables here

[processes]
  app = "npm run start"

[[services]]
  protocol = "tcp"
  internal_port = 3000
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1

  [services.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [[services.tcp_checks]]
    interval = "10s"
    timeout = "2s"
    grace_period = "5s"
    restart_limit = 6

  [[services.http_checks]]
    interval = "30s"
    timeout = "3s"
    grace_period = "5s"
    method = "GET"
    path = "/api/health"
    protocol = "http"
    tls_skip_verify = false
    
    [services.http_checks.headers]
      X-Health-Check = "fly.io"

[[statics]]
  guest_path = "/app/apps/admin-remix/public"
  url_prefix = "/static"

[metrics]
  path = "/api/metrics"

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512