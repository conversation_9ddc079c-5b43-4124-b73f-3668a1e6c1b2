# Production Deployment Guide for SaaS Shopify App

## How URL Handling Works for SaaS Apps

Unlike single-merchant private apps, SaaS Shopify apps need to handle dynamic URLs because:

1. **Multiple Stores**: Your app serves many different Shopify stores
2. **Dynamic OAuth**: Each store has its own OAuth flow with callbacks
3. **Webhook URLs**: Must be accessible from Shopify's servers
4. **App Bridge**: Needs to know the app's URL for embedded navigation

## URL Configuration Strategy

### Development
```bash
# Shopify CLI handles everything automatically
shopify app dev

# Or manually with ngrok for testing
SHOPIFY_APP_URL=https://your-tunnel.ngrok.io
```

### Production
Your app automatically detects its URL from the request headers:

```javascript
// The app uses the Host header to determine its URL
// No hardcoded URLs needed!
const appUrl = `https://${request.headers.get('host')}`;
```

## Production Deployment Options

### Option 1: Fly.io (Recommended)

```toml
# fly.toml
app = "bundleforge"

[env]
  PORT = "8080"
  NODE_ENV = "production"

[http_service]
  internal_port = 8080
  force_https = true
```

Deploy:
```bash
fly launch

# Automated secret setup (recommended)
./scripts/setup-production-env.sh --platform fly

# Then set your Shopify credentials
fly secrets set SHOPIFY_API_KEY=xxx SHOPIFY_API_SECRET=yyy

fly deploy
```

### Option 2: Heroku

```json
// app.json
{
  "name": "bundleforge",
  "env": {
    "NODE_ENV": {
      "value": "production"
    }
  },
  "formation": {
    "web": {
      "quantity": 1,
      "size": "standard-1x"
    }
  }
}
```

Deploy:
```bash
heroku create bundleforge

# Automated secret setup (recommended)
./scripts/setup-production-env.sh --platform heroku

# Then set your Shopify credentials
heroku config:set SHOPIFY_API_KEY=xxx SHOPIFY_API_SECRET=yyy

git push heroku main
```

### Option 3: AWS/Docker

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY . .
RUN npm ci --production
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## Essential Production Setup

### 1. Automated Environment Setup

**🚀 Quick Setup (Recommended)**

Use our automated setup script to generate secure secrets:

```bash
# For Fly.io
./scripts/setup-production-env.sh --platform fly

# For Heroku
./scripts/setup-production-env.sh --platform heroku

# For Docker/Manual
./scripts/setup-production-env.sh --platform docker

# Windows PowerShell
.\scripts\setup-production-env.ps1 -Platform fly
```

**Manual Setup (if needed)**

```bash
# Required
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SESSION_SECRET=$(openssl rand -hex 32)  # Auto-generated secure secret
NODE_ENV=production

# Optional but recommended
REDIS_URL=redis://your-redis-instance:6379
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=info
```

### 2. Update Shopify App Settings

In your Partner Dashboard:

1. **App URL**: `https://your-domain.com`
2. **Allowed redirection URL(s)**:
   ```
   https://your-domain.com/auth/callback
   https://your-domain.com/auth
   ```
3. **GDPR webhooks** (automatically registered by the app):
   - Customer data request: `https://your-domain.com/webhooks/customers/data_request`
   - Customer redact: `https://your-domain.com/webhooks/customers/redact`
   - Shop redact: `https://your-domain.com/webhooks/shop/redact`

### 3. Domain Configuration

For custom domains:
```bash
# Add CNAME record
bundleforge.yourcompany.com -> your-app.fly.dev

# Or A record for static IP
bundleforge.yourcompany.com -> 123.456.789.0
```

### 4. SSL/HTTPS (Required)

Most platforms handle this automatically. For custom setups:
- Use Let's Encrypt for free SSL
- Or use Cloudflare for SSL + CDN

## How the App Handles Multiple Shops

1. **Shop Isolation**: Each shop's data is isolated using their domain as a key
2. **Session Management**: Sessions include shop domain for proper routing
3. **Webhook Verification**: HMAC verification ensures requests are from Shopify
4. **Metafield Namespacing**: Each shop's data stored in their own metafields

## Production Checklist

- [ ] Set all required environment variables
- [ ] Configure Redis for session storage
- [ ] Update Partner Dashboard URLs
- [ ] Enable HTTPS/SSL
- [ ] Set up error monitoring (Sentry)
- [ ] Configure logging
- [ ] Test OAuth flow with a test store
- [ ] Verify webhooks are received
- [ ] Load test with multiple stores

## Common Production Issues

### Issue: OAuth redirect mismatch
**Solution**: Ensure Partner Dashboard URLs exactly match your production domain

### Issue: Webhooks not received
**Solution**: Check that your app is publicly accessible (not behind auth)

### Issue: Sessions lost between requests
**Solution**: Ensure Redis is properly configured and accessible

### Issue: "App must be served over HTTPS"
**Solution**: Enable force_https in your hosting platform

## Scaling Considerations

1. **Redis Cluster**: For high traffic, use Redis cluster for sessions
2. **CDN**: Use Cloudflare for static assets
3. **Worker Dynos**: Scale horizontally for more concurrent shops
4. **Rate Limiting**: Implement per-shop rate limits
5. **Background Jobs**: Use queues for heavy operations

## Security Best Practices

1. **Never expose API secrets** in client-side code
2. **Always verify webhooks** with HMAC
3. **Use HTTPS everywhere**
4. **Implement rate limiting** per shop
5. **Log security events** for audit trails
6. **Regular dependency updates**

## Automated Setup Scripts

The BundleForge app includes automated setup scripts to generate secure session secrets:

### PowerShell (Windows)
```powershell
# Basic usage
.\scripts\setup-production-env.ps1 -Platform fly

# Options
.\scripts\setup-production-env.ps1 -Platform heroku -Force
.\scripts\setup-production-env.ps1 -Platform docker -DryRun
```

### Bash (Linux/macOS)
```bash
# Basic usage
./scripts/setup-production-env.sh --platform fly

# Options
./scripts/setup-production-env.sh --platform heroku --force
./scripts/setup-production-env.sh --platform docker --dry-run
```

**Supported Platforms:**
- `fly` - Fly.io deployment
- `heroku` - Heroku deployment
- `docker` - Docker/containerized deployment
- `manual` - Display secrets for manual setup

**Script Features:**
- ✅ Generates cryptographically secure SESSION_SECRET
- ✅ Automatically sets secrets in deployment platform
- ✅ Checks for existing secrets (won't overwrite unless --force)
- ✅ Provides clear next steps
- ✅ Dry-run mode to preview changes

The key insight: **Your SaaS app doesn't need hardcoded URLs**. It dynamically determines its URL from the request, making it portable across any hosting platform!