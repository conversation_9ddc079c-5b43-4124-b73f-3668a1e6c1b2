import type { ActionFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Page,
  Card,
  FormLayout,
  TextField,
  Button,
  Select,
  ResourceList,
  ResourceItem,
  Text,
  Badge,
  ButtonGroup,
  Banner,
  Spinner,
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { PlusIcon, DeleteIcon } from "@shopify/polaris-icons";
import { authenticatedLoader, authenticatedAction } from "../libs/auth.middleware.server";
import { BundleService } from "../libs/bundle.server";
import { validateBundle, formatZodError } from "../libs/validation.server";
import { z } from "zod";
import { ErrorBoundary } from "../components/ErrorBoundary";
import { getCSRFToken, csrfProtection } from "../libs/csrf.server";
import { bundleCreateRateLimiter } from "../libs/rate-limit.server";

// Mock product data for MMVP
const mockProducts = [
  { id: "1", title: "Product A", price: "29.99", image: null },
  { id: "2", title: "Product B", price: "39.99", image: null },
  { id: "3", title: "Product C", price: "19.99", image: null },
  { id: "4", title: "Product D", price: "49.99", image: null },
  { id: "5", title: "Product E", price: "24.99", image: null },
];

export const loader = authenticatedLoader(async ({ request, auth }) => {
  try {
    const { client, session } = auth;
    
    // Get CSRF token
    const { token: csrf, cookie } = await getCSRFToken(request, session.shop);

    // Fetch products from Shopify API
    const query = `
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              variants(first: 1) {
                edges {
                  node {
                    id
                    price
                  }
                }
              }
              featuredImage {
                url
                altText
              }
            }
          }
        }
      }
    `;

    const response = await client.request(query, {
      variables: { first: 50 }
    });

    const products = response.data?.products?.edges?.map((edge: any) => ({
      id: edge.node.id,
      title: edge.node.title,
      price: edge.node.variants.edges[0]?.node.price || "0.00",
      image: edge.node.featuredImage?.url || null,
    })) || [];

    return json({ products, csrf }, {
      headers: {
        "Set-Cookie": cookie,
      },
    });
  } catch (error) {
    console.error("Failed to load products:", error);

    // Get CSRF token even on error
    const { token: csrf, cookie } = await getCSRFToken(request);
    
    // Return mock products if there's an error
    return json({ products: mockProducts, csrf }, {
      headers: {
        "Set-Cookie": cookie,
      },
    });
  }
});

export const action = authenticatedAction(async ({ request, auth }) => {
  // Apply rate limiting
  const rateLimitResult = await bundleCreateRateLimiter(request);
  if (rateLimitResult instanceof Response) {
    return rateLimitResult;
  }
  
  // Verify CSRF token
  const csrfError = await csrfProtection(request, auth.session.shop);
  if (csrfError) {
    return csrfError;
  }
  
  try {
    const bundleService = new BundleService(auth.session.shop, auth.session.accessToken);

    const formData = await request.formData();
    const name = formData.get("name") as string;
    const discountPercentage = formData.get("discountPercentage") as string;
    const products = formData.get("products") as string;

    let parsedProducts;
    try {
      parsedProducts = JSON.parse(products || "[]");
    } catch {
      return json({ errors: { products: "Invalid products data" } }, { status: 400 });
    }

    // Validate input using Zod
    try {
      const validatedData = validateBundle({
        name,
        products: parsedProducts,
        discount_percentage: Number(discountPercentage),
        active: true,
      });

      // Create bundle using the validated data
      await bundleService.createBundle(validatedData);

      return redirect("/bundles");
    } catch (error) {
      if (error instanceof z.ZodError) {
        return json({ errors: { general: formatZodError(error) } }, { status: 400 });
      }
      throw error;
    }
  } catch (error) {
    console.error("Failed to create bundle:", error);
    return json({
      errors: { general: error instanceof Error ? error.message : "Failed to create bundle. Please try again." }
    }, { status: 500 });
  }
});

interface BundleProduct {
  product_id: string;
  quantity: number;
}

export { ErrorBoundary };

export default function NewBundle() {
  const { products, csrf } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [name, setName] = useState("");
  const [discountPercentage, setDiscountPercentage] = useState("10");
  const [selectedProducts, setSelectedProducts] = useState<BundleProduct[]>([]);
  const [showProductPicker, setShowProductPicker] = useState(false);

  const handleNameChange = useCallback((value: string) => {
    setName(value);
  }, []);

  const handleDiscountChange = useCallback((value: string) => {
    setDiscountPercentage(value);
  }, []);

  const handleAddProduct = useCallback((productId: string) => {
    const existingProduct = selectedProducts.find(p => p.product_id === productId);
    if (existingProduct) {
      setSelectedProducts(prev => 
        prev.map(p => 
          p.product_id === productId 
            ? { ...p, quantity: p.quantity + 1 }
            : p
        )
      );
    } else {
      setSelectedProducts(prev => [...prev, { product_id: productId, quantity: 1 }]);
    }
    setShowProductPicker(false);
  }, [selectedProducts]);

  const handleRemoveProduct = useCallback((productId: string) => {
    setSelectedProducts(prev => prev.filter(p => p.product_id !== productId));
  }, []);

  const handleQuantityChange = useCallback((productId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveProduct(productId);
      return;
    }
    setSelectedProducts(prev => 
      prev.map(p => 
        p.product_id === productId 
          ? { ...p, quantity }
          : p
      )
    );
  }, [handleRemoveProduct]);

  const getProductById = (id: string) => {
    return products.find(p => p.id === id);
  };

  const availableProducts = products.filter(
    p => !selectedProducts.some(sp => sp.product_id === p.id)
  );

  return (
    <Page
      title="Create Bundle"
      backAction={{ url: "/bundles" }}
      primaryAction={{
        content: isSubmitting ? "Creating..." : "Create Bundle",
        loading: isSubmitting,
        disabled: isSubmitting,
        onAction: () => {
          const form = document.getElementById("bundle-form") as HTMLFormElement;
          form?.requestSubmit();
        },
      }}
    >
      {actionData?.errors && (
        <Banner status="critical" title="Please fix the following errors:">
          <ul>
            {Object.entries(actionData.errors).map(([field, error]) => (
              <li key={field}>{error}</li>
            ))}
          </ul>
        </Banner>
      )}

      <Form method="post" id="bundle-form">
        <FormLayout>
          <Card>
            <FormLayout>
              <TextField
                label="Bundle Name"
                value={name}
                onChange={handleNameChange}
                error={actionData?.errors?.name}
                placeholder="e.g., Summer Essentials Bundle"
                autoComplete="off"
              />
              
              <TextField
                label="Discount Percentage"
                type="number"
                value={discountPercentage}
                onChange={handleDiscountChange}
                error={actionData?.errors?.discountPercentage}
                suffix="%"
                min="0"
                max="100"
                step="1"
                helpText="Percentage discount applied to the total bundle price"
              />
            </FormLayout>
          </Card>

          <Card>
            <div style={{ marginBottom: "1rem" }}>
              <Text variant="headingMd" as="h2">
                Bundle Products
              </Text>
              <Text variant="bodyMd" as="p" color="subdued">
                Add products to this bundle
              </Text>
            </div>

            {selectedProducts.length > 0 && (
              <div style={{ marginBottom: "1rem" }}>
                <ResourceList
                  resourceName={{ singular: "product", plural: "products" }}
                  items={selectedProducts.map(sp => {
                    const product = getProductById(sp.product_id);
                    return {
                      id: sp.product_id,
                      product,
                      quantity: sp.quantity,
                    };
                  })}
                  renderItem={(item) => {
                    const { id, product, quantity } = item;
                    if (!product) return null;

                    return (
                      <ResourceItem
                        id={id}
                        media={
                          <div
                            style={{
                              width: "40px",
                              height: "40px",
                              backgroundColor: "#f6f6f7",
                              borderRadius: "4px",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text variant="bodySm" color="subdued">
                              IMG
                            </Text>
                          </div>
                        }
                      >
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                          <div>
                            <Text variant="bodyMd" fontWeight="medium">
                              {product.title}
                            </Text>
                            <Text variant="bodySm" color="subdued">
                              ${product.price}
                            </Text>
                          </div>
                          <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
                            <TextField
                              label="Quantity"
                              labelHidden
                              type="number"
                              value={quantity.toString()}
                              onChange={(value) => handleQuantityChange(id, parseInt(value) || 0)}
                              min="1"
                              style={{ width: "80px" }}
                            />
                            <Button
                              icon={DeleteIcon}
                              variant="tertiary"
                              tone="critical"
                              onClick={() => handleRemoveProduct(id)}
                              accessibilityLabel={`Remove ${product.title}`}
                            />
                          </div>
                        </div>
                      </ResourceItem>
                    );
                  }}
                />
              </div>
            )}

            {!showProductPicker ? (
              <Button
                icon={PlusIcon}
                onClick={() => setShowProductPicker(true)}
                disabled={availableProducts.length === 0}
              >
                Add Product
              </Button>
            ) : (
              <Card>
                <div style={{ marginBottom: "1rem" }}>
                  <Text variant="headingSm" as="h3">
                    Select a Product
                  </Text>
                </div>
                <ResourceList
                  resourceName={{ singular: "product", plural: "products" }}
                  items={availableProducts}
                  renderItem={(product) => (
                    <ResourceItem
                      id={product.id}
                      media={
                        <div
                          style={{
                            width: "40px",
                            height: "40px",
                            backgroundColor: "#f6f6f7",
                            borderRadius: "4px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text variant="bodySm" color="subdued">
                            IMG
                          </Text>
                        </div>
                      }
                      onClick={() => handleAddProduct(product.id)}
                    >
                      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                        <div>
                          <Text variant="bodyMd" fontWeight="medium">
                            {product.title}
                          </Text>
                          <Text variant="bodySm" color="subdued">
                            ${product.price}
                          </Text>
                        </div>
                        <Button size="micro">Add</Button>
                      </div>
                    </ResourceItem>
                  )}
                />
                <div style={{ marginTop: "1rem" }}>
                  <Button onClick={() => setShowProductPicker(false)}>
                    Cancel
                  </Button>
                </div>
              </Card>
            )}

            {actionData?.errors?.products && (
              <div style={{ marginTop: "1rem" }}>
                <Text variant="bodySm" color="critical">
                  {actionData.errors.products}
                </Text>
              </div>
            )}
          </Card>
        </FormLayout>

        {/* Hidden inputs for form submission */}
        <input
          type="hidden"
          name="_csrf"
          value={csrf}
        />
        <input
          type="hidden"
          name="name"
          value={name}
        />
        <input
          type="hidden"
          name="discountPercentage"
          value={discountPercentage}
        />
        <input
          type="hidden"
          name="products"
          value={JSON.stringify(selectedProducts)}
        />
      </Form>
    </Page>
  );
}