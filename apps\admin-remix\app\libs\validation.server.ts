import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Enhanced sanitization to prevent XSS
function sanitizeString(str: string): string {
  // First, use DOMPurify for comprehensive XSS protection
  const cleaned = DOMPurify.sanitize(str, { 
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
  });
  
  // Additional sanitization for extra safety
  return cleaned
    .replace(/[<>"'&]/g, (char) => {
      // HTML entity encoding
      const entities: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;',
      };
      return entities[char] || char;
    })
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/\\x[0-9a-fA-F]{2}/g, '') // Remove hex escapes
    .replace(/\\u[0-9a-fA-F]{4}/g, '') // Remove unicode escapes
    .trim();
}

// Validate and sanitize URLs
function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Only allow http(s) protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }
    return parsed.toString();
  } catch {
    throw new Error('Invalid URL');
  }
}

// Custom Zod refinements
const sanitizedString = z.string().transform(sanitizeString);

// Bundle validation schemas
export const BundleProductSchema = z.object({
  product_id: z.string()
    .regex(/^gid:\/\/shopify\/Product\/\d+$/, 'Invalid product ID format')
    .max(100, 'Product ID too long'),
  quantity: z.number()
    .int()
    .positive('Quantity must be positive')
    .max(1000, 'Quantity too large'),
});

export const CreateBundleSchema = z.object({
  name: z.string()
    .min(1, 'Bundle name is required')
    .max(255, 'Bundle name must be less than 255 characters')
    .pipe(sanitizedString)
    .refine((val) => !/[\x00-\x1F\x7F]/.test(val), 'Bundle name contains invalid characters'),
  products: z.array(BundleProductSchema)
    .min(1, 'At least one product is required')
    .max(50, 'Maximum 50 products per bundle'),
  discount_percentage: z.number()
    .min(0, 'Discount cannot be negative')
    .max(100, 'Discount cannot exceed 100%')
    .refine((val) => Number.isFinite(val), 'Invalid discount value'),
  active: z.boolean().default(false),
  ghost_product_id: z.string()
    .regex(/^gid:\/\/shopify\/Product\/\d+$/, 'Invalid ghost product ID format')
    .max(100, 'Ghost product ID too long')
    .optional(),
});

export const UpdateBundleSchema = CreateBundleSchema.partial();

// Shop validation with enhanced security
export const ShopDomainSchema = z.string()
  .min(1, 'Shop domain is required')
  .max(100, 'Shop domain too long')
  .regex(/^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/, 'Invalid shop domain format')
  .refine((val) => !val.includes('..'), 'Invalid shop domain')
  .transform((val) => val.toLowerCase());

// Pagination validation
export const PaginationSchema = z.object({
  first: z.number().int().min(1).max(250).optional(),
  after: z.string().optional(),
});

// Webhook validation schemas
export const WebhookHeadersSchema = z.object({
  'x-shopify-topic': z.string(),
  'x-shopify-hmac-sha256': z.string(),
  'x-shopify-shop-domain': ShopDomainSchema.optional(),
  'x-shopify-api-version': z.string().optional(),
  'x-shopify-webhook-id': z.string().optional(),
});

// Product webhook schema
export const ProductWebhookSchema = z.object({
  id: z.number(),
  title: z.string(),
  handle: z.string(),
  product_type: z.string().optional(),
  vendor: z.string().optional(),
  status: z.enum(['active', 'archived', 'draft']),
  created_at: z.string(),
  updated_at: z.string(),
  variants: z.array(z.object({
    id: z.number(),
    product_id: z.number(),
    title: z.string(),
    price: z.string(),
    inventory_quantity: z.number().optional(),
  })).optional(),
});

// GDPR webhook schemas
export const GDPRCustomerSchema = z.object({
  id: z.number(),
  email: z.string().email().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
});

export const GDPRDataRequestSchema = z.object({
  shop_domain: ShopDomainSchema,
  customer: GDPRCustomerSchema.optional(),
  data_request: z.object({
    id: z.string(),
  }).optional(),
});

export const GDPRCustomerRedactSchema = z.object({
  shop_domain: ShopDomainSchema,
  customer: GDPRCustomerSchema.optional(),
  orders_to_redact: z.array(z.number()).optional(),
  data_request: z.object({
    id: z.string(),
  }).optional(),
});

export const GDPRShopRedactSchema = z.object({
  shop_domain: ShopDomainSchema,
  data_request: z.object({
    id: z.string(),
  }).optional(),
});

export const OrderWebhookSchema = z.object({
  id: z.number(),
  email: z.string().email().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  total_price: z.string(),
  currency: z.string(),
  line_items: z.array(z.object({
    id: z.number(),
    product_id: z.number(),
    variant_id: z.number(),
    quantity: z.number(),
    price: z.string(),
    title: z.string(),
    variant_title: z.string().nullable(),
    properties: z.array(z.object({
      name: z.string(),
      value: z.string(),
    })).optional(),
  })),
  customer: z.object({
    id: z.number(),
    email: z.string().email().optional(),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
  }).optional(),
});

// GDPR webhook schemas
export const CustomerRedactSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
  customer: z.object({
    id: z.number(),
    email: z.string().email(),
    phone: z.string().optional(),
  }),
  orders_to_redact: z.array(z.number()).optional(),
});

export const ShopRedactSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
});

export const DataRequestSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
  customer: z.object({
    id: z.number(),
    email: z.string().email(),
    phone: z.string().optional(),
  }),
  orders_requested: z.array(z.number()).optional(),
  data_request: z.object({
    id: z.number(),
  }),
});

// Validation helper functions
export function validateBundle(data: unknown) {
  return CreateBundleSchema.parse(data);
}

export function validateBundleUpdate(data: unknown) {
  return UpdateBundleSchema.parse(data);
}

export function validatePagination(params: URLSearchParams) {
  return PaginationSchema.parse({
    first: params.get('first') ? parseInt(params.get('first')!) : undefined,
    after: params.get('after') || undefined,
  });
}

// Error formatting helper with sanitization
export function formatZodError(error: z.ZodError): string {
  return error.errors
    .map(err => {
      const path = err.path.join('.');
      const message = sanitizeString(err.message);
      return `${path}: ${message}`;
    })
    .join(', ');
}

// Request body size validation
export function validateRequestSize(request: Request, maxSizeBytes: number = 1048576): boolean {
  const contentLength = request.headers.get('content-length');
  if (contentLength && parseInt(contentLength) > maxSizeBytes) {
    return false;
  }
  return true;
}

// SQL injection prevention helper (for any raw queries)
export function escapeSqlIdentifier(identifier: string): string {
  // Remove any non-alphanumeric characters except underscores
  return identifier.replace(/[^a-zA-Z0-9_]/g, '');
}

// Validate JSON safely
export function safeJsonParse<T>(json: string): T | null {
  try {
    // Limit JSON size to prevent DoS
    if (json.length > 1048576) { // 1MB limit
      throw new Error('JSON too large');
    }
    return JSON.parse(json);
  } catch {
    return null;
  }
}