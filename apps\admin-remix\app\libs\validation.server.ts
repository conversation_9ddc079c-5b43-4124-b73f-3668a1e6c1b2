import { z } from 'zod';

// Sanitize string to prevent XSS
function sanitizeString(str: string): string {
  return str
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

// Custom Zod refinements
const sanitizedString = z.string().transform(sanitizeString);

// Bundle validation schemas
export const BundleProductSchema = z.object({
  product_id: z.string().regex(/^gid:\/\/shopify\/Product\/\d+$/, 'Invalid product ID format'),
  quantity: z.number().int().positive('Quantity must be positive'),
});

export const CreateBundleSchema = z.object({
  name: sanitizedString
    .min(1, 'Bundle name is required')
    .max(255, 'Bundle name must be less than 255 characters'),
  products: z.array(BundleProductSchema)
    .min(1, 'At least one product is required')
    .max(50, 'Maximum 50 products per bundle'),
  discount_percentage: z.number()
    .min(0, 'Discount cannot be negative')
    .max(100, 'Discount cannot exceed 100%'),
  active: z.boolean().default(false),
  ghost_product_id: z.string()
    .regex(/^gid:\/\/shopify\/Product\/\d+$/, 'Invalid ghost product ID format')
    .optional(),
});

export const UpdateBundleSchema = CreateBundleSchema.partial();

// Shop validation
export const ShopDomainSchema = z.string()
  .regex(/^[a-zA-Z0-9-]+\.myshopify\.com$/, 'Invalid shop domain format');

// Pagination validation
export const PaginationSchema = z.object({
  first: z.number().int().min(1).max(250).optional(),
  after: z.string().optional(),
});

// Webhook validation schemas
export const WebhookHeadersSchema = z.object({
  'x-shopify-topic': z.string(),
  'x-shopify-hmac-sha256': z.string(),
  'x-shopify-shop-domain': ShopDomainSchema,
  'x-shopify-api-version': z.string().optional(),
  'x-shopify-webhook-id': z.string().optional(),
});

export const OrderWebhookSchema = z.object({
  id: z.number(),
  email: z.string().email().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  total_price: z.string(),
  currency: z.string(),
  line_items: z.array(z.object({
    id: z.number(),
    product_id: z.number(),
    variant_id: z.number(),
    quantity: z.number(),
    price: z.string(),
    title: z.string(),
    variant_title: z.string().nullable(),
    properties: z.array(z.object({
      name: z.string(),
      value: z.string(),
    })).optional(),
  })),
  customer: z.object({
    id: z.number(),
    email: z.string().email().optional(),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
  }).optional(),
});

// GDPR webhook schemas
export const CustomerRedactSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
  customer: z.object({
    id: z.number(),
    email: z.string().email(),
    phone: z.string().optional(),
  }),
  orders_to_redact: z.array(z.number()).optional(),
});

export const ShopRedactSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
});

export const DataRequestSchema = z.object({
  shop_id: z.number(),
  shop_domain: ShopDomainSchema,
  customer: z.object({
    id: z.number(),
    email: z.string().email(),
    phone: z.string().optional(),
  }),
  orders_requested: z.array(z.number()).optional(),
  data_request: z.object({
    id: z.number(),
  }),
});

// Validation helper functions
export function validateBundle(data: unknown) {
  return CreateBundleSchema.parse(data);
}

export function validateBundleUpdate(data: unknown) {
  return UpdateBundleSchema.parse(data);
}

export function validatePagination(params: URLSearchParams) {
  return PaginationSchema.parse({
    first: params.get('first') ? parseInt(params.get('first')!) : undefined,
    after: params.get('after') || undefined,
  });
}

// Error formatting helper
export function formatZodError(error: z.ZodError): string {
  return error.errors
    .map(err => `${err.path.join('.')}: ${err.message}`)
    .join(', ');
}