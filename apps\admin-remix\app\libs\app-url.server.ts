/**
 * Dynamic URL handling for SaaS Shopify apps
 * Works in both development and production environments
 */

export function getAppUrl(request: Request): string {
  // In production, use the actual host from the request
  if (process.env.NODE_ENV === 'production') {
    const host = request.headers.get('host');
    const protocol = request.headers.get('x-forwarded-proto') || 'https';
    
    if (!host) {
      throw new Error('Unable to determine app URL: missing host header');
    }
    
    return `${protocol}://${host}`;
  }
  
  // In development, use the environment variable or Shopify CLI tunnel
  if (process.env.SHOPIFY_APP_URL) {
    return process.env.SHOPIFY_APP_URL;
  }
  
  // Fallback for local development
  const host = request.headers.get('host') || 'localhost:3000';
  return `http://${host}`;
}

export function getOAuthCallbackUrl(request: Request): string {
  return `${getAppUrl(request)}/auth/callback`;
}

export function getWebhookUrl(request: Request, topic: string): string {
  const appUrl = getAppUrl(request);
  const webhookPath = topic.toLowerCase().replace(/\//g, '.');
  return `${appUrl}/webhooks/${webhookPath}`;
}

// For use in non-request contexts (e.g., background jobs)
export function getAppUrlFromEnv(): string {
  if (process.env.SHOPIFY_APP_URL) {
    return process.env.SHOPIFY_APP_URL;
  }
  
  if (process.env.FLY_APP_NAME) {
    // Fly.io deployment
    return `https://${process.env.FLY_APP_NAME}.fly.dev`;
  }
  
  if (process.env.HEROKU_APP_NAME) {
    // Heroku deployment
    return `https://${process.env.HEROKU_APP_NAME}.herokuapp.com`;
  }
  
  if (process.env.VERCEL_URL) {
    // Vercel deployment
    return `https://${process.env.VERCEL_URL}`;
  }
  
  if (process.env.RENDER_EXTERNAL_URL) {
    // Render.com deployment
    return process.env.RENDER_EXTERNAL_URL;
  }
  
  // Default fallback
  return 'http://localhost:3000';
}