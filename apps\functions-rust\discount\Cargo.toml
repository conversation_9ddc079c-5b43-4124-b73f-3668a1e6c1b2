[package]
name = "discount"
version = "0.1.0"
edition = "2021"
authors = ["BundleForge Team"]
description = "Shopify Discount Function for BundleForge - Advanced bundle discount calculations"
license = "MIT"
repository = "https://github.com/bundleforge/bundleforge"

[lib]
crate-type = ["cdylib"]

[dependencies]
shopify_function = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
bundleforge-shared = { workspace = true }
chrono = { version = "0.4", features = ["serde"] }
regex = "1.0"
once_cell = "1.19"
thiserror = "1.0"
tracing = "0.1"
uuid = { version = "1.0", features = ["v4", "serde"] }

[profile.release]
opt-level = "s"
lto = true
codegen-units = 1
panic = "abort"
strip = "symbols"

[profile.dev]
opt-level = 0
debug = true