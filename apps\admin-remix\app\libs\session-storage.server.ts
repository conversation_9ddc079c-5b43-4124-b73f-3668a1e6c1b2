import { createClient } from 'redis';
import type { ShopifySession } from './auth.server';

// Redis client configuration
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
const sessionTTL = 86400; // 24 hours in seconds

// Create Redis client with lazy connection
let redisClient: ReturnType<typeof createClient> | null = null;

async function getRedisClient() {
  if (!redisClient) {
    redisClient = createClient({
      url: redisUrl,
      socket: {
        connectTimeout: 10000,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            console.error('Redis connection failed after 10 retries');
            return new Error('Redis connection failed');
          }
          return Math.min(retries * 100, 3000);
        }
      }
    });

    redisClient.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('Redis Client Connected');
    });

    await redisClient.connect();
  }
  
  return redisClient;
}

// Session storage interface
export interface SessionStorage {
  save(sessionId: string, session: ShopifySession): Promise<void>;
  get(sessionId: string): Promise<ShopifySession | undefined>;
  delete(sessionId: string): Promise<void>;
  deleteByShop(shop: string): Promise<void>;
  cleanup(): Promise<void>;
}

// Redis implementation
class RedisSessionStorage implements SessionStorage {
  private getKey(sessionId: string): string {
    return `session:${sessionId}`;
  }

  async save(sessionId: string, session: ShopifySession): Promise<void> {
    try {
      const client = await getRedisClient();
      const key = this.getKey(sessionId);
      const value = JSON.stringify(session);
      
      // Set with TTL
      await client.setEx(key, sessionTTL, value);
      
      // Also maintain a set of sessions per shop for cleanup
      await client.sAdd(`shop:${session.shop}:sessions`, sessionId);
      await client.expire(`shop:${session.shop}:sessions`, sessionTTL);
    } catch (error) {
      console.error('Failed to save session to Redis:', error);
      throw new Error('Session storage failed');
    }
  }

  async get(sessionId: string): Promise<ShopifySession | undefined> {
    try {
      const client = await getRedisClient();
      const key = this.getKey(sessionId);
      const value = await client.get(key);
      
      if (!value) {
        return undefined;
      }
      
      const session = JSON.parse(value) as ShopifySession;
      
      // Check if session is expired
      if (session.expires && new Date(session.expires) < new Date()) {
        await this.delete(sessionId);
        return undefined;
      }
      
      return session;
    } catch (error) {
      console.error('Failed to get session from Redis:', error);
      return undefined;
    }
  }

  async delete(sessionId: string): Promise<void> {
    try {
      const client = await getRedisClient();
      const key = this.getKey(sessionId);
      
      // Get session to find shop
      const session = await this.get(sessionId);
      if (session) {
        await client.sRem(`shop:${session.shop}:sessions`, sessionId);
      }
      
      await client.del(key);
    } catch (error) {
      console.error('Failed to delete session from Redis:', error);
    }
  }

  async deleteByShop(shop: string): Promise<void> {
    try {
      const client = await getRedisClient();
      
      // Get all sessions for this shop
      const sessionIds = await client.sMembers(`shop:${shop}:sessions`);
      
      if (sessionIds.length > 0) {
        // Delete all sessions
        const keys = sessionIds.map(id => this.getKey(id));
        await client.del(keys);
      }
      
      // Delete the shop's session set
      await client.del(`shop:${shop}:sessions`);
    } catch (error) {
      console.error('Failed to delete shop sessions from Redis:', error);
    }
  }

  async cleanup(): Promise<void> {
    try {
      const client = await getRedisClient();
      
      // Redis automatically handles TTL expiration
      // This method can be used for additional cleanup if needed
      console.log('Session cleanup completed');
    } catch (error) {
      console.error('Failed to cleanup sessions:', error);
    }
  }
}

// In-memory fallback for development/testing
class InMemorySessionStorage implements SessionStorage {
  private sessions = new Map<string, ShopifySession>();
  private shopSessions = new Map<string, Set<string>>();

  async save(sessionId: string, session: ShopifySession): Promise<void> {
    this.sessions.set(sessionId, session);
    
    // Track sessions by shop
    if (!this.shopSessions.has(session.shop)) {
      this.shopSessions.set(session.shop, new Set());
    }
    this.shopSessions.get(session.shop)!.add(sessionId);
    
    // Schedule cleanup if session has expiry
    if (session.expires) {
      const ttl = new Date(session.expires).getTime() - Date.now();
      if (ttl > 0) {
        setTimeout(() => this.delete(sessionId), ttl);
      }
    }
  }

  async get(sessionId: string): Promise<ShopifySession | undefined> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      return undefined;
    }
    
    // Check if session is expired
    if (session.expires && new Date(session.expires) < new Date()) {
      await this.delete(sessionId);
      return undefined;
    }
    
    return session;
  }

  async delete(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      const shopSessions = this.shopSessions.get(session.shop);
      if (shopSessions) {
        shopSessions.delete(sessionId);
        if (shopSessions.size === 0) {
          this.shopSessions.delete(session.shop);
        }
      }
    }
    this.sessions.delete(sessionId);
  }

  async deleteByShop(shop: string): Promise<void> {
    const sessionIds = this.shopSessions.get(shop);
    if (sessionIds) {
      for (const sessionId of sessionIds) {
        this.sessions.delete(sessionId);
      }
      this.shopSessions.delete(shop);
    }
  }

  async cleanup(): Promise<void> {
    const now = new Date();
    const expiredSessions: string[] = [];
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expires && new Date(session.expires) < now) {
        expiredSessions.push(sessionId);
      }
    }
    
    for (const sessionId of expiredSessions) {
      await this.delete(sessionId);
    }
  }
}

// Export singleton instance
let sessionStorage: SessionStorage;

if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
  sessionStorage = new RedisSessionStorage();
} else {
  console.warn('Using in-memory session storage. This is not suitable for production!');
  sessionStorage = new InMemorySessionStorage();
}

export { sessionStorage };

// Cleanup job - run periodically
if (process.env.NODE_ENV === 'production') {
  setInterval(async () => {
    try {
      await sessionStorage.cleanup();
    } catch (error) {
      console.error('Session cleanup failed:', error);
    }
  }, 60 * 60 * 1000); // Run every hour
}