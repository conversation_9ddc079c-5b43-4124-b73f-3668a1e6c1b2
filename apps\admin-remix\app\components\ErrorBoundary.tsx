import { useRouteError, isRouteErrorResponse } from "@remix-run/react";
import { Banner, Page, Card, Text } from "@shopify/polaris";

export function ErrorBoundary() {
  const error = useRouteError();
  
  // Log error for monitoring
  console.error("Error boundary caught:", error);

  if (isRouteErrorResponse(error)) {
    return (
      <Page>
        <Card>
          <Banner
            title={`Error ${error.status}: ${error.statusText}`}
            status="critical"
          >
            {error.data && (
              <Text as="p" variant="bodyMd">
                {typeof error.data === "string" ? error.data : JSON.stringify(error.data)}
              </Text>
            )}
          </Banner>
        </Card>
      </Page>
    );
  }

  let errorMessage = "An unexpected error occurred";
  let errorDetails = "";

  if (error instanceof Error) {
    errorMessage = error.message;
    errorDetails = error.stack || "";
  } else if (typeof error === "string") {
    errorMessage = error;
  }

  return (
    <Page>
      <Card>
        <Banner
          title="Unexpected Error"
          status="critical"
          action={{
            content: "Reload page",
            onAction: () => window.location.reload(),
          }}
        >
          <Text as="p" variant="bodyMd">
            {errorMessage}
          </Text>
          {process.env.NODE_ENV === "development" && errorDetails && (
            <details style={{ marginTop: "1rem" }}>
              <summary>Error details (development only)</summary>
              <pre style={{ 
                marginTop: "0.5rem", 
                padding: "1rem", 
                background: "#f6f6f6", 
                borderRadius: "4px",
                fontSize: "12px",
                overflow: "auto"
              }}>
                {errorDetails}
              </pre>
            </details>
          )}
        </Banner>
      </Card>
    </Page>
  );
}

export function BundleErrorBoundary() {
  const error = useRouteError();
  
  if (isRouteErrorResponse(error) && error.status === 404) {
    return (
      <Page>
        <Card>
          <Banner
            title="Bundle not found"
            status="warning"
            action={{
              content: "View all bundles",
              url: "/bundles",
            }}
          >
            The bundle you're looking for doesn't exist or has been deleted.
          </Banner>
        </Card>
      </Page>
    );
  }

  // Fallback to default error boundary
  return <ErrorBoundary />;
}