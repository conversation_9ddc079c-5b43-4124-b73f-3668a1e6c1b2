import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Select,
  Checkbox,
  Button,
  Banner,
  Text,
  BlockStack,
  Spinner,
} from "@shopify/polaris";
import { authenticate } from "~/libs/auth.server";
import { createMetafieldsService } from "@bundleforge/shopify-metafields";
import { handleError } from "~/libs/error-handler.server";

interface AppPreferences {
  bundleDiscountType: string;
  defaultDiscountPercentage: number;
  enableAnalytics: boolean;
  enableABTesting: boolean;
  maxBundleSize: number;
  autoCreateBundles: boolean;
  notificationEmail: string;
  currency: string;
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    // Load preferences from Shopify metafields
    let preferences: AppPreferences;

    try {
      const savedPreferences = await getAppPreferences(session);

      if (savedPreferences) {
        preferences = savedPreferences;
      } else {
        // Default preferences for new installations
        preferences = {
          bundleDiscountType: "percentage",
          defaultDiscountPercentage: 10,
          enableAnalytics: true,
          enableABTesting: false,
          maxBundleSize: 5,
          autoCreateBundles: false,
          notificationEmail: "",
          currency: "USD",
        };
      }
    } catch (metafieldError) {
      console.error("Error loading preferences from metafields:", metafieldError);

      // Fallback to defaults if metafield loading fails
      preferences = {
        bundleDiscountType: "percentage",
        defaultDiscountPercentage: 10,
        enableAnalytics: true,
        enableABTesting: false,
        maxBundleSize: 5,
        autoCreateBundles: false,
        notificationEmail: "",
        currency: "USD",
      };
    }

    return json({ preferences, shop });
  } catch (error) {
    console.error("Error in preferences loader:", error);
    return handleError(error);
  }
}

export async function action({ request }: ActionFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    const formData = await request.formData();

    // Validate form data
    const bundleDiscountType = formData.get("bundleDiscountType") as string;
    const defaultDiscountPercentage = Number(formData.get("defaultDiscountPercentage"));
    const maxBundleSize = Number(formData.get("maxBundleSize"));
    const notificationEmail = formData.get("notificationEmail") as string;

    // Basic validation
    if (!bundleDiscountType || !["percentage", "fixed", "bxgy"].includes(bundleDiscountType)) {
      return json({
        success: false,
        message: "Invalid discount type selected."
      }, { status: 400 });
    }

    if (isNaN(defaultDiscountPercentage) || defaultDiscountPercentage < 0 || defaultDiscountPercentage > 100) {
      return json({
        success: false,
        message: "Discount percentage must be between 0 and 100."
      }, { status: 400 });
    }

    if (isNaN(maxBundleSize) || maxBundleSize < 2 || maxBundleSize > 20) {
      return json({
        success: false,
        message: "Max bundle size must be between 2 and 20."
      }, { status: 400 });
    }

    if (notificationEmail && !isValidEmail(notificationEmail)) {
      return json({
        success: false,
        message: "Please enter a valid email address."
      }, { status: 400 });
    }

    const preferences: AppPreferences = {
      bundleDiscountType,
      defaultDiscountPercentage,
      enableAnalytics: formData.get("enableAnalytics") === "on",
      enableABTesting: formData.get("enableABTesting") === "on",
      maxBundleSize,
      autoCreateBundles: formData.get("autoCreateBundles") === "on",
      notificationEmail,
      currency: formData.get("currency") as string,
    };

    // Save preferences to Shopify metafields
    await saveAppPreferences(session, preferences);

    console.log(`Preferences saved successfully for shop: ${shop}`, preferences);

    return json({
      success: true,
      message: "Preferences saved successfully!"
    });
  } catch (error) {
    console.error("Error saving preferences:", error);
    return json({
      success: false,
      message: "Failed to save preferences. Please try again."
    }, { status: 500 });
  }
}

// Email validation helper
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export default function AppPreferences() {
  const { preferences, shop } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();

  const discountTypeOptions = [
    { label: "Percentage", value: "percentage" },
    { label: "Fixed Amount", value: "fixed" },
    { label: "Buy X Get Y", value: "bxgy" },
  ];

  const currencyOptions = [
    { label: "USD - US Dollar", value: "USD" },
    { label: "EUR - Euro", value: "EUR" },
    { label: "GBP - British Pound", value: "GBP" },
    { label: "CAD - Canadian Dollar", value: "CAD" },
    { label: "AUD - Australian Dollar", value: "AUD" },
  ];

  return (
    <Page
      title="App Preferences"
      subtitle={`Configure BundleForge settings for ${shop}`}
      backAction={{ content: "Back to Dashboard", url: "/app" }}
    >
      <Layout>
        <Layout.Section>
          {actionData?.success && (
            <Banner status="success" title="Success">
              <p>{actionData.message}</p>
            </Banner>
          )}
          
          {actionData?.success === false && (
            <Banner status="critical" title="Error">
              <p>{actionData.message}</p>
            </Banner>
          )}

          <Form method="post">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    Bundle Settings
                  </Text>
                  
                  <FormLayout>
                    <Select
                      label="Default Discount Type"
                      options={discountTypeOptions}
                      value={preferences.bundleDiscountType}
                      name="bundleDiscountType"
                      helpText="Choose the default discount type for new bundles"
                    />
                    
                    <TextField
                      label="Default Discount Percentage"
                      type="number"
                      value={preferences.defaultDiscountPercentage.toString()}
                      name="defaultDiscountPercentage"
                      suffix="%"
                      min="0"
                      max="100"
                      helpText="Default discount percentage for new bundles"
                      autoComplete="off"
                    />
                    
                    <TextField
                      label="Maximum Bundle Size"
                      type="number"
                      value={preferences.maxBundleSize.toString()}
                      name="maxBundleSize"
                      suffix="products"
                      min="2"
                      max="20"
                      helpText="Maximum number of products allowed in a bundle"
                      autoComplete="off"
                    />
                    
                    <Select
                      label="Store Currency"
                      options={currencyOptions}
                      value={preferences.currency}
                      name="currency"
                      helpText="Primary currency for bundle pricing"
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    Automation Settings
                  </Text>
                  
                  <FormLayout>
                    <Checkbox
                      label="Auto-create bundles from frequently bought together"
                      checked={preferences.autoCreateBundles}
                      name="autoCreateBundles"
                      helpText="Automatically suggest and create bundles based on purchase patterns"
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    Analytics & Testing
                  </Text>
                  
                  <FormLayout>
                    <Checkbox
                      label="Enable Analytics Tracking"
                      checked={preferences.enableAnalytics}
                      name="enableAnalytics"
                      helpText="Track bundle performance and conversion metrics"
                    />
                    
                    <Checkbox
                      label="Enable A/B Testing"
                      checked={preferences.enableABTesting}
                      name="enableABTesting"
                      helpText="Allow A/B testing of different bundle configurations"
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2">
                    Notifications
                  </Text>
                  
                  <FormLayout>
                    <TextField
                      label="Notification Email"
                      type="email"
                      value={preferences.notificationEmail}
                      name="notificationEmail"
                      helpText="Email address for important app notifications"
                      autoComplete="email"
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              <Card>
                <Button submit variant="primary" size="large">
                  Save Preferences
                </Button>
              </Card>
            </BlockStack>
          </Form>
        </Layout.Section>
      </Layout>
    </Page>
  );
}


