import { type LoaderFunctionArgs } from '@remix-run/node';
import fs from 'fs';
import path from 'path';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Serve the bundleforge-tracker.js file
  const filePath = path.join(process.cwd(), 'public', 'bundleforge-tracker.js');
  
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    
    return new Response(fileContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Access-Control-Allow-Origin': '*', // Allow cross-origin requests
      },
    });
  } catch (error) {
    console.error('Error serving tracker script:', error);
    return new Response('// BundleForge tracker script not found', {
      status: 404,
      headers: {
        'Content-Type': 'application/javascript',
      },
    });
  }
};