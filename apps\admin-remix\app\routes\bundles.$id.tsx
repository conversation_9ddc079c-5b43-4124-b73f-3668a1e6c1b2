import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, <PERSON> } from "@remix-run/react";
import {
  Page,
  Card,
  Text,
  Badge,
  ResourceList,
  ResourceItem,
  ButtonGroup,
  Button,
} from "@shopify/polaris";
import { EditIcon } from "@shopify/polaris-icons";
import { requireAuth } from "../libs/auth.server";
import { BundleService } from "../libs/bundle.server";

// Mock data for MMVP
const mockProducts = [
  { id: "1", title: "Product A", price: "29.99", image: null },
  { id: "2", title: "Product B", price: "39.99", image: null },
  { id: "3", title: "Product C", price: "19.99", image: null },
  { id: "4", title: "Product D", price: "49.99", image: null },
  { id: "5", title: "Product E", price: "24.99", image: null },
];

const mockBundles = [
  {
    id: "1",
    name: "Summer Essentials",
    status: "active",
    products: [
      { product_id: "1", quantity: 2 },
      { product_id: "2", quantity: 1 }
    ],
    discount: "15%",
    discount_percentage: 15,
    created: "2024-01-15",
    active: true,
  },
  {
    id: "2",
    name: "Winter Bundle",
    status: "draft",
    products: [
      { product_id: "3", quantity: 1 },
      { product_id: "4", quantity: 1 }
    ],
    discount: "20%",
    discount_percentage: 20,
    created: "2024-01-18",
    active: false,
  },
];

export async function loader({ params, request }: LoaderFunctionArgs) {
  try {
    const { session, client } = await requireAuth(request);
    const bundleService = new BundleService(session.shop, session.accessToken);
    const bundleId = params.id;

    if (!bundleId) {
      throw new Response("Bundle ID is required", { status: 400 });
    }

    // Fetch bundle from metafields
    const bundle = await bundleService.getBundleById(bundleId);

    if (!bundle) {
      throw new Response("Bundle not found", { status: 404 });
    }

    // Fetch products from Shopify API
    const query = `
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              variants(first: 1) {
                edges {
                  node {
                    id
                    price
                  }
                }
              }
              featuredImage {
                url
                altText
              }
            }
          }
        }
      }
    `;

    const response = await client.request(query, {
      variables: { first: 250 }
    });

    const products = response.data?.products?.edges?.map((edge: any) => ({
      id: edge.node.id,
      title: edge.node.title,
      price: edge.node.variants.edges[0]?.node.price || "0.00",
      image: edge.node.featuredImage?.url || null,
    })) || [];

    return json({ bundle, products });
  } catch (error) {
    console.error("Failed to load bundle:", error);

    // Return mock data if there's an error
    const bundle = mockBundles.find(b => b.id === params.id);
    if (!bundle) {
      throw new Response("Bundle not found", { status: 404 });
    }
    return json({ bundle, products: mockProducts });
  }
}

export default function BundleDetail() {
  const { bundle, products } = useLoaderData<typeof loader>();

  const getProductById = (id: string) => {
    return products.find(p => p.id === id);
  };

  const calculateBundlePrice = () => {
    const totalPrice = bundle.products.reduce((sum, bundleProduct) => {
      const product = getProductById(bundleProduct.product_id);
      if (product) {
        return sum + (parseFloat(product.price) * bundleProduct.quantity);
      }
      return sum;
    }, 0);
    
    const discountAmount = totalPrice * (bundle.discount_percentage / 100);
    return {
      originalPrice: totalPrice.toFixed(2),
      discountAmount: discountAmount.toFixed(2),
      finalPrice: (totalPrice - discountAmount).toFixed(2),
    };
  };

  const pricing = calculateBundlePrice();

  return (
    <Page
      title={bundle.name}
      backAction={{ url: "/bundles" }}
      primaryAction={{
        content: "Edit Bundle",
        icon: EditIcon,
        url: `/bundles/${bundle.id}/edit`,
      }}
    >
      <div style={{ display: "grid", gap: "1rem" }}>
        {/* Bundle Overview */}
        <Card>
          <div style={{ display: "grid", gap: "1rem" }}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <Text variant="headingMd" as="h2">
                Bundle Details
              </Text>
              <Badge tone={bundle.active ? "success" : "info"}>
                {bundle.active ? "Active" : "Inactive"}
              </Badge>
            </div>
            
            <div style={{ display: "grid", gap: "0.5rem" }}>
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Text variant="bodyMd" color="subdued">
                  Discount:
                </Text>
                <Text variant="bodyMd" fontWeight="medium">
                  {bundle.discount_percentage}%
                </Text>
              </div>
              
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Text variant="bodyMd" color="subdued">
                  Created:
                </Text>
                <Text variant="bodyMd">
                  {new Date(bundle.created).toLocaleDateString()}
                </Text>
              </div>
            </div>
          </div>
        </Card>

        {/* Pricing Information */}
        <Card>
          <div style={{ display: "grid", gap: "1rem" }}>
            <Text variant="headingMd" as="h2">
              Pricing
            </Text>
            
            <div style={{ display: "grid", gap: "0.5rem" }}>
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Text variant="bodyMd" color="subdued">
                  Original Price:
                </Text>
                <Text variant="bodyMd">
                  ${pricing.originalPrice}
                </Text>
              </div>
              
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Text variant="bodyMd" color="subdued">
                  Discount Amount:
                </Text>
                <Text variant="bodyMd" color="critical">
                  -${pricing.discountAmount}
                </Text>
              </div>
              
              <div style={{ display: "flex", justifyContent: "space-between", paddingTop: "0.5rem", borderTop: "1px solid #e1e3e5" }}>
                <Text variant="bodyMd" fontWeight="semibold">
                  Bundle Price:
                </Text>
                <Text variant="bodyMd" fontWeight="semibold" color="success">
                  ${pricing.finalPrice}
                </Text>
              </div>
            </div>
          </div>
        </Card>

        {/* Bundle Products */}
        <Card>
          <div style={{ marginBottom: "1rem" }}>
            <Text variant="headingMd" as="h2">
              Bundle Products ({bundle.products.length})
            </Text>
            <Text variant="bodyMd" color="subdued">
              Products included in this bundle
            </Text>
          </div>

          <ResourceList
            resourceName={{ singular: "product", plural: "products" }}
            items={bundle.products.map(bp => {
              const product = getProductById(bp.product_id);
              return {
                id: bp.product_id,
                product,
                quantity: bp.quantity,
              };
            })}
            renderItem={(item) => {
              const { id, product, quantity } = item;
              if (!product) return null;

              const itemTotal = (parseFloat(product.price) * quantity).toFixed(2);

              return (
                <ResourceItem
                  id={id}
                  media={
                    <div
                      style={{
                        width: "50px",
                        height: "50px",
                        backgroundColor: "#f6f6f7",
                        borderRadius: "8px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Text variant="bodySm" color="subdued">
                        IMG
                      </Text>
                    </div>
                  }
                >
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%" }}>
                    <div>
                      <Text variant="bodyMd" fontWeight="medium">
                        {product.title}
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        ${product.price} each
                      </Text>
                    </div>
                    <div style={{ textAlign: "right" }}>
                      <Text variant="bodyMd" fontWeight="medium">
                        Qty: {quantity}
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        Total: ${itemTotal}
                      </Text>
                    </div>
                  </div>
                </ResourceItem>
              );
            }}
          />
        </Card>
      </div>
    </Page>
  );
}