import type { HeadersFunction } from "@remix-run/node";

/**
 * Security headers configuration
 */
export const securityHeaders: Record<string, string> = {
  // Content Security Policy
  "Content-Security-Policy": [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.shopify.com https://*.myshopify.com", // Shopify App Bridge requires inline scripts
    "style-src 'self' 'unsafe-inline' https://cdn.shopify.com", // Polaris requires inline styles
    "img-src 'self' data: https: blob:",
    "font-src 'self' data: https://cdn.shopify.com",
    "connect-src 'self' https://*.myshopify.com wss://*.myshopify.com https://cdn.shopify.com",
    "frame-src 'self' https://*.myshopify.com",
    "frame-ancestors https://*.myshopify.com https://admin.shopify.com", // Required for embedded apps
    "base-uri 'self'",
    "form-action 'self'",
    "object-src 'none'",
    "upgrade-insecure-requests",
  ].join("; "),
  
  // Prevent clickjacking (except from Shopify admin)
  "X-Frame-Options": "ALLOW-FROM https://admin.shopify.com",
  
  // XSS Protection (legacy browsers)
  "X-XSS-Protection": "1; mode=block",
  
  // Prevent MIME type sniffing
  "X-Content-Type-Options": "nosniff",
  
  // Referrer Policy
  "Referrer-Policy": "strict-origin-when-cross-origin",
  
  // Permissions Policy
  "Permissions-Policy": [
    "accelerometer=()",
    "camera=()",
    "geolocation=()",
    "gyroscope=()",
    "magnetometer=()",
    "microphone=()",
    "payment=()",
    "usb=()",
  ].join(", "),
  
  // Strict Transport Security (HSTS)
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
  
  // Cross-Origin policies
  "Cross-Origin-Opener-Policy": "same-origin-allow-popups", // Allow Shopify OAuth popups
  "Cross-Origin-Resource-Policy": "cross-origin", // Allow Shopify to load resources
  "Cross-Origin-Embedder-Policy": "unsafe-none", // Required for Shopify embedded apps
};

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(headers: Headers): void {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    headers.set(key, value);
  });
}

/**
 * Remix headers function with security headers
 */
export const createSecurityHeaders: HeadersFunction = ({ loaderHeaders, actionHeaders }) => {
  const headers = new Headers(loaderHeaders || actionHeaders);
  applySecurityHeaders(headers);
  return headers;
};

/**
 * Content Security Policy for specific routes
 */
export function getCSPForRoute(pathname: string): string {
  // More permissive CSP for auth routes
  if (pathname.startsWith("/auth")) {
    return [
      "default-src 'self' https://*.myshopify.com",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.shopify.com https://*.myshopify.com",
      "style-src 'self' 'unsafe-inline' https://cdn.shopify.com",
      "img-src 'self' data: https: blob:",
      "font-src 'self' data: https://cdn.shopify.com",
      "connect-src 'self' https://*.myshopify.com wss://*.myshopify.com https://cdn.shopify.com",
      "frame-src 'self' https://*.myshopify.com",
      "frame-ancestors 'none'", // Auth pages should not be embedded
      "form-action 'self' https://*.myshopify.com",
      "base-uri 'self'",
      "object-src 'none'",
    ].join("; ");
  }
  
  // Default CSP
  return securityHeaders["Content-Security-Policy"];
}

/**
 * Nonce generator for inline scripts
 */
export function generateNonce(): string {
  const crypto = require("crypto");
  return crypto.randomBytes(16).toString("base64");
}

/**
 * Apply nonce to CSP
 */
export function applyNonceToCSP(csp: string, nonce: string): string {
  return csp.replace(
    "script-src 'self' 'unsafe-inline'",
    `script-src 'self' 'nonce-${nonce}'`
  );
}