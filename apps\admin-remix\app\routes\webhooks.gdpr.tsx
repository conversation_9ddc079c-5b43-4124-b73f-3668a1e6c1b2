import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook } from "~/libs/auth.server";
import { 
  GDPRDataRequestSchema, 
  GDPRCustomerRedactSchema, 
  GDPRShopRedactSchema 
} from "~/libs/validation.server";
import { handleError, Logger } from "~/libs/error-handler.server";
import { handleDataRequest, handleDataDeletion } from "~/libs/auth.server";

const logger = Logger.getInstance();

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  const topic = request.headers.get("x-shopify-topic");
  const shop = request.headers.get("x-shopify-shop-domain");
  
  if (!hmacHeader || !topic) {
    return new Response("Missing required headers", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    logger.error("Invalid HMAC for GDPR webhook", undefined, {
      shop,
      metadata: { topic }
    });
    return new Response("Invalid HMAC", { status: 401 });
  }

  const correlationId = logger.info(`Processing GDPR webhook: ${topic}`, {
    shop,
    metadata: { topic }
  });

  try {
    const data = JSON.parse(rawBody);
    
    switch (topic) {
      case "customers/data_request": {
        const validated = GDPRDataRequestSchema.parse(data);
        const customerData = await handleDataRequest(
          validated.shop_domain,
          validated.customer?.id?.toString()
        );
        
        logger.info("GDPR data request completed", {
          correlationId,
          shop: validated.shop_domain,
          metadata: {
            customerId: validated.customer?.id,
            dataSize: JSON.stringify(customerData).length
          }
        });
        
        // In production, you would send this data to a secure endpoint
        // For now, we log that the data was collected successfully
        return json({ 
          success: true, 
          message: "Data request processed",
          correlationId 
        });
      }
      
      case "customers/redact": {
        const validated = GDPRCustomerRedactSchema.parse(data);
        await handleDataDeletion(
          validated.shop_domain,
          validated.customer?.id?.toString()
        );
        
        logger.info("GDPR customer data redacted", {
          correlationId,
          shop: validated.shop_domain,
          metadata: {
            customerId: validated.customer?.id,
            ordersRedacted: validated.orders_to_redact?.length || 0
          }
        });
        
        return json({ 
          success: true, 
          message: "Customer data redacted",
          correlationId 
        });
      }
      
      case "shop/redact": {
        const validated = GDPRShopRedactSchema.parse(data);
        await handleDataDeletion(validated.shop_domain);
        
        logger.info("GDPR shop data redacted", {
          correlationId,
          shop: validated.shop_domain,
          metadata: {
            requestId: validated.data_request?.id
          }
        });
        
        return json({ 
          success: true, 
          message: "Shop data redacted",
          correlationId 
        });
      }
      
      default:
        logger.warn("Unknown GDPR webhook topic", {
          correlationId,
          shop,
          metadata: { topic }
        });
        return new Response("Unknown topic", { status: 400 });
    }
  } catch (error) {
    logger.error("GDPR webhook processing failed", error, {
      correlationId,
      shop,
      metadata: { topic }
    });
    return handleError(error, { correlationId, shop });
  }
}