import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook, handleDataRequest, handleDataDeletion } from "../libs/auth.server";
import { GDPRDataRequestSchema, GDPRCustomerRedactSchema, GDPRShopRedactSchema } from "../libs/validation.server";
import { handleError } from "../libs/error-handler.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const payload = JSON.parse(rawBody);
    const shop = payload.shop_domain || request.headers.get("x-shopify-shop-domain");
    const topic = request.headers.get("x-shopify-topic");

    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Route to appropriate GDPR handler based on topic
    switch (topic) {
      case "customers/data_request":
        return await handleCustomerDataRequest(payload, shop);
      
      case "customers/redact":
        return await handleCustomerRedact(payload, shop);
      
      case "shop/redact":
        return await handleShopRedact(payload, shop);
      
      default:
        return new Response("Unknown GDPR topic", { status: 400 });
    }
  } catch (error) {
    console.error("Error handling GDPR webhook:", error);
    return handleError(error);
  }
}

async function handleCustomerDataRequest(payload: any, shop: string) {
  // Validate payload
  const validatedPayload = GDPRDataRequestSchema.parse(payload);
  const customerId = validatedPayload.customer?.id?.toString();
  const customerEmail = validatedPayload.customer?.email;

  // Handle data request
  const customerData = await handleDataRequest(shop, customerId);
  
  // Log data request for compliance tracking
  console.log(`GDPR data request for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    customerId,
    customerEmail,
    requestId: payload.data_request?.id,
  });

  return json({ 
    success: true,
    message: "Data request processed",
    data: customerData 
  }, { status: 200 });
}

async function handleCustomerRedact(payload: any, shop: string) {
  const customerId = payload.customer?.id?.toString();
  const customerEmail = payload.customer?.email;
  const ordersToRedact = payload.orders_to_redact || [];
  
  // Handle data deletion
  await handleDataDeletion(shop, customerId);
  
  // Log data deletion for compliance tracking
  console.log(`GDPR data redaction for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    customerId,
    customerEmail,
    ordersToRedact: ordersToRedact.length,
    requestId: payload.data_request?.id,
  });

  return json({ 
    success: true,
    message: "Customer data redacted successfully"
  }, { status: 200 });
}

async function handleShopRedact(payload: any, shop: string) {
  // Handle complete shop data deletion
  await handleShopDataDeletion(shop);
  
  // Log shop data deletion for compliance tracking
  console.log(`GDPR shop data redaction for shop: ${shop}`, {
    timestamp: new Date().toISOString(),
    shop,
    requestId: payload.data_request?.id,
  });

  return json({ 
    success: true,
    message: "Shop data redacted successfully"
  }, { status: 200 });
}

// Handle complete shop data deletion
async function handleShopDataDeletion(shop: string): Promise<void> {
  try {
    // TODO: Implement comprehensive data deletion
    // This should include:
    
    // 1. Delete all bundle configurations
    console.log(`Deleting bundle configurations for shop: ${shop}`);
    
    // 2. Delete all analytics data
    console.log(`Deleting analytics data for shop: ${shop}`);
    
    // 3. Delete all A/B testing data
    console.log(`Deleting A/B testing data for shop: ${shop}`);
    
    // 4. Delete all customer preferences
    console.log(`Deleting customer preferences for shop: ${shop}`);
    
    // 5. Delete all cached data
    console.log(`Clearing cached data for shop: ${shop}`);
    
    // 6. Delete all session data
    console.log(`Deleting session data for shop: ${shop}`);
    
    // 7. Delete all webhook configurations
    console.log(`Removing webhook configurations for shop: ${shop}`);
    
    // 8. Delete all function configurations
    console.log(`Removing function configurations for shop: ${shop}`);
    
    // 9. Delete all audit logs (after required retention period)
    console.log(`Scheduling audit log deletion for shop: ${shop}`);
    
    // 10. Update compliance records
    console.log(`Updating compliance records for shop: ${shop}`);
    
    console.log(`Shop data deletion completed for: ${shop}`);
  } catch (error) {
    console.error(`Error deleting shop data for ${shop}:`, error);
    throw error;
  }
}
