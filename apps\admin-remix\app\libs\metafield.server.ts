import { createShopifyClient } from "./shopify.server";
import type { ShopifySession } from "./auth.server";
import { cache, CACHE_TTL } from "./cache.server";

export interface MetafieldInput {
  namespace: string;
  key: string;
  value: string;
  type: string;
  ownerId?: string;
  ownerResource?: string;
}

export interface MetafieldResponse {
  id: string;
  namespace: string;
  key: string;
  value: string;
  type: string;
  ownerId?: string;
  ownerResource?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BatchMetafieldOperation {
  operation: 'create' | 'update' | 'delete';
  metafield: MetafieldInput;
  id?: string;
}

/**
 * Comprehensive Metafield Service for BundleForge
 * Handles all metafield operations with proper error handling and batching
 */
export class MetafieldService {
  private client: ReturnType<typeof createShopifyClient>;
  private shop: string;

  constructor(session: ShopifySession) {
    this.client = createShopifyClient(session.shop, session.accessToken);
    this.shop = session.shop;
  }

  /**
   * Create a single metafield
   */
  async create(metafield: MetafieldInput): Promise<MetafieldResponse> {
    this.validateMetafield(metafield);

    try {
      const mutation = `
        mutation metafieldCreate($metafield: MetafieldInput!) {
          metafieldCreate(metafield: $metafield) {
            metafield {
              id
              namespace
              key
              value
              type
              ownerId
              ownerResource
              createdAt
              updatedAt
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        metafield: {
          ...metafield,
          ownerId: metafield.ownerId || `gid://shopify/Shop/${this.getShopId()}`,
          ownerResource: metafield.ownerResource || 'SHOP',
        },
      };

      const response = await this.client.graphql(mutation, variables);
      const data = response.body as any;

      if (data.data?.metafieldCreate?.userErrors?.length > 0) {
        throw new Error(`Metafield creation failed: ${data.data.metafieldCreate.userErrors.map((e: any) => e.message).join(', ')}`);
      }

      const createdMetafield = data.data.metafieldCreate.metafield;

      // Invalidate related cache entries
      await this.invalidateMetafieldCache(metafield.namespace, metafield.key, metafield.ownerId);

      return createdMetafield;
    } catch (error) {
      console.error('Error creating metafield:', error);
      throw error;
    }
  }

  /**
   * Update an existing metafield
   */
  async update(id: string, metafield: Partial<MetafieldInput>): Promise<MetafieldResponse> {
    try {
      const mutation = `
        mutation metafieldUpdate($metafield: MetafieldInput!) {
          metafieldUpdate(metafield: $metafield) {
            metafield {
              id
              namespace
              key
              value
              type
              ownerId
              ownerResource
              createdAt
              updatedAt
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        metafield: {
          id,
          ...metafield,
        },
      };

      const response = await this.client.graphql(mutation, variables);
      const data = response.body as any;

      if (data.data?.metafieldUpdate?.userErrors?.length > 0) {
        throw new Error(`Metafield update failed: ${data.data.metafieldUpdate.userErrors.map((e: any) => e.message).join(', ')}`);
      }

      const updatedMetafield = data.data.metafieldUpdate.metafield;

      // Invalidate related cache entries
      if (metafield.namespace || metafield.key) {
        await this.invalidateMetafieldCache(
          metafield.namespace || updatedMetafield.namespace,
          metafield.key || updatedMetafield.key,
          metafield.ownerId || updatedMetafield.ownerId
        );
      }

      return updatedMetafield;
    } catch (error) {
      console.error('Error updating metafield:', error);
      throw error;
    }
  }

  /**
   * Delete a metafield
   */
  async delete(id: string): Promise<boolean> {
    try {
      const mutation = `
        mutation metafieldDelete($input: MetafieldDeleteInput!) {
          metafieldDelete(input: $input) {
            deletedId
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: { id },
      };

      const response = await this.client.graphql(mutation, variables);
      const data = response.body as any;

      if (data.data?.metafieldDelete?.userErrors?.length > 0) {
        throw new Error(`Metafield deletion failed: ${data.data.metafieldDelete.userErrors.map((e: any) => e.message).join(', ')}`);
      }

      return !!data.data.metafieldDelete.deletedId;
    } catch (error) {
      console.error('Error deleting metafield:', error);
      throw error;
    }
  }

  /**
   * Get metafields by namespace and key (with caching)
   */
  async get(namespace: string, key?: string, ownerId?: string): Promise<MetafieldResponse[]> {
    // Create cache key
    const cacheKey = `metafield:${namespace}:${key || 'all'}:${ownerId || 'global'}`;

    // Try to get from cache first
    const cached = await cache.get<MetafieldResponse[]>(cacheKey, {
      namespace: this.shop,
      ttl: CACHE_TTL.MEDIUM
    });

    if (cached) {
      return cached;
    }

    try {
      const query = `
        query getMetafields($namespace: String!, $key: String, $ownerId: ID) {
          metafields(
            namespace: $namespace
            key: $key
            ownerId: $ownerId
            first: 250
          ) {
            edges {
              node {
                id
                namespace
                key
                value
                type
                ownerId
                ownerResource
                createdAt
                updatedAt
              }
            }
          }
        }
      `;

      const variables = {
        namespace,
        key,
        ownerId: ownerId || `gid://shopify/Shop/${this.getShopId()}`,
      };

      const response = await this.client.graphql(query, variables);
      const data = response.body as any;

      const metafields = data.data?.metafields?.edges?.map((edge: any) => edge.node) || [];

      // Cache the result
      await cache.set(cacheKey, metafields, {
        namespace: this.shop,
        ttl: CACHE_TTL.MEDIUM
      });

      return metafields;
    } catch (error) {
      console.error('Error getting metafields:', error);
      throw error;
    }
  }

  /**
   * Batch operations for multiple metafields
   */
  async batchSet(metafields: MetafieldInput[]): Promise<MetafieldResponse[]> {
    try {
      const mutation = `
        mutation metafieldsSet($metafields: [MetafieldInput!]!) {
          metafieldsSet(metafields: $metafields) {
            metafields {
              id
              namespace
              key
              value
              type
              ownerId
              ownerResource
              createdAt
              updatedAt
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        metafields: metafields.map(metafield => ({
          ...metafield,
          ownerId: metafield.ownerId || `gid://shopify/Shop/${this.getShopId()}`,
          ownerResource: metafield.ownerResource || 'SHOP',
        })),
      };

      const response = await this.client.graphql(mutation, variables);
      const data = response.body as any;

      if (data.data?.metafieldsSet?.userErrors?.length > 0) {
        throw new Error(`Batch metafield operation failed: ${data.data.metafieldsSet.userErrors.map((e: any) => e.message).join(', ')}`);
      }

      return data.data.metafieldsSet.metafields;
    } catch (error) {
      console.error('Error in batch metafield operation:', error);
      throw error;
    }
  }

  /**
   * Upsert a metafield (create or update)
   */
  async upsert(metafield: MetafieldInput): Promise<MetafieldResponse> {
    try {
      // First, try to find existing metafield
      const existing = await this.get(metafield.namespace, metafield.key, metafield.ownerId);
      
      if (existing.length > 0) {
        // Update existing
        return await this.update(existing[0].id, metafield);
      } else {
        // Create new
        return await this.create(metafield);
      }
    } catch (error) {
      console.error('Error upserting metafield:', error);
      throw error;
    }
  }

  /**
   * Get shop ID from shop domain
   */
  private getShopId(): string {
    // Extract shop ID from domain (simplified)
    // In production, you might want to cache this or get it from the session
    return this.shop.replace('.myshopify.com', '');
  }

  /**
   * Invalidate metafield cache entries
   */
  private async invalidateMetafieldCache(namespace: string, key?: string, ownerId?: string): Promise<void> {
    try {
      // Invalidate specific cache entry
      const cacheKey = `metafield:${namespace}:${key || 'all'}:${ownerId || 'global'}`;
      await cache.delete(cacheKey, { namespace: this.shop });

      // Also invalidate broader patterns that might include this metafield
      await cache.invalidatePattern(`metafield:${namespace}:*`, this.shop);

      // If this is a bundle-related metafield, invalidate bundle cache too
      if (namespace === 'bundleforge') {
        await cache.invalidatePattern('bundle:*', this.shop);
      }
    } catch (error) {
      console.error('Error invalidating metafield cache:', error);
      // Don't throw - cache invalidation failure shouldn't break the operation
    }
  }

  /**
   * Validate metafield data before operations
   */
  private validateMetafield(metafield: MetafieldInput): void {
    if (!metafield.namespace || !metafield.key || !metafield.value || !metafield.type) {
      throw new Error('Metafield must have namespace, key, value, and type');
    }

    if (metafield.namespace.length > 20 || metafield.key.length > 30) {
      throw new Error('Namespace must be ≤20 chars, key must be ≤30 chars');
    }

    if (metafield.value.length > 65535) {
      throw new Error('Metafield value must be ≤65535 characters');
    }
  }
}

/**
 * Helper functions for common metafield operations
 */

export async function getAppPreferences(session: ShopifySession): Promise<any> {
  const service = new MetafieldService(session);
  const metafields = await service.get('bundleforge', 'app_preferences');
  
  if (metafields.length > 0) {
    return JSON.parse(metafields[0].value);
  }
  
  return null;
}

export async function saveAppPreferences(session: ShopifySession, preferences: any): Promise<void> {
  const service = new MetafieldService(session);
  await service.upsert({
    namespace: 'bundleforge',
    key: 'app_preferences',
    value: JSON.stringify(preferences),
    type: 'json',
  });
}

export async function getBundleConfiguration(session: ShopifySession, bundleId: string): Promise<any> {
  const service = new MetafieldService(session);
  const metafields = await service.get('bundleforge', `bundle_${bundleId}`);
  
  if (metafields.length > 0) {
    return JSON.parse(metafields[0].value);
  }
  
  return null;
}

export async function saveBundleConfiguration(session: ShopifySession, bundleId: string, config: any): Promise<void> {
  const service = new MetafieldService(session);
  await service.upsert({
    namespace: 'bundleforge',
    key: `bundle_${bundleId}`,
    value: JSON.stringify(config),
    type: 'json',
  });
}

// Bundle-specific interfaces
export interface BundleProduct {
  product_id: string;
  quantity: number;
  variant_id?: string;
}

export interface BundleConfig {
  id: string;
  name: string;
  products: BundleProduct[];
  discount_percentage: number;
  discount_type: 'percentage' | 'fixed' | 'bxgy';
  active: boolean;
  created_at?: string;
  updated_at?: string;
  ab_test_config?: {
    enabled: boolean;
    variant_a: any;
    variant_b: any;
  };
}

export interface AnalyticsData {
  bundle_id: string;
  conversions: number;
  revenue: number;
  views: number;
  cart_adds: number;
  last_updated: string;
  conversion_funnel: {
    views: number;
    cart_adds: number;
    checkouts: number;
    purchases: number;
  };
  ab_test_data: Record<string, any>;
}

// Bundle management functions
export async function getAllBundles(session: ShopifySession): Promise<BundleConfig[]> {
  const service = new MetafieldService(session);
  const metafields = await service.get('bundleforge', 'bundles');

  if (metafields.length > 0) {
    return JSON.parse(metafields[0].value);
  }

  return [];
}

export async function saveAllBundles(session: ShopifySession, bundles: BundleConfig[]): Promise<void> {
  const service = new MetafieldService(session);
  await service.upsert({
    namespace: 'bundleforge',
    key: 'bundles',
    value: JSON.stringify(bundles),
    type: 'json',
  });
}

export async function createOrUpdateBundle(session: ShopifySession, bundle: BundleConfig): Promise<void> {
  const bundles = await getAllBundles(session);
  const existingIndex = bundles.findIndex(b => b.id === bundle.id);

  if (existingIndex >= 0) {
    bundles[existingIndex] = { ...bundle, updated_at: new Date().toISOString() };
  } else {
    bundles.push({ ...bundle, created_at: new Date().toISOString(), updated_at: new Date().toISOString() });
  }

  await saveAllBundles(session, bundles);
}

export async function deleteBundle(session: ShopifySession, bundleId: string): Promise<void> {
  const bundles = await getAllBundles(session);
  const filteredBundles = bundles.filter(b => b.id !== bundleId);
  await saveAllBundles(session, filteredBundles);
}

export async function getActiveBundles(session: ShopifySession): Promise<BundleConfig[]> {
  const bundles = await getAllBundles(session);
  return bundles.filter(bundle => bundle.active);
}

export async function getBundleById(session: ShopifySession, bundleId: string): Promise<BundleConfig | null> {
  const bundles = await getAllBundles(session);
  return bundles.find(bundle => bundle.id === bundleId) || null;
}

// Product-specific bundle functions
export async function getBundleFromProduct(session: ShopifySession, productId: string): Promise<BundleConfig | null> {
  const service = new MetafieldService(session);
  const metafields = await service.get('bundleforge', 'bundle_config', `gid://shopify/Product/${productId}`);

  if (metafields.length > 0) {
    return JSON.parse(metafields[0].value);
  }

  return null;
}

export async function saveBundleToProduct(session: ShopifySession, productId: string, bundle: BundleConfig): Promise<void> {
  const service = new MetafieldService(session);
  await service.upsert({
    namespace: 'bundleforge',
    key: 'bundle_config',
    value: JSON.stringify(bundle),
    type: 'json',
    ownerId: `gid://shopify/Product/${productId}`,
    ownerResource: 'PRODUCT',
  });
}

export async function findBundlesContainingProduct(session: ShopifySession, productId: string): Promise<BundleConfig[]> {
  const bundles = await getAllBundles(session);
  return bundles.filter(bundle =>
    bundle.products.some(product => product.product_id === productId)
  );
}

// Analytics functions
export async function getAnalyticsData(session: ShopifySession, bundleId?: string): Promise<AnalyticsData[]> {
  const service = new MetafieldService(session);
  const key = bundleId ? `analytics_${bundleId}` : 'analytics_all';
  const metafields = await service.get('bundleforge', key);

  if (metafields.length > 0) {
    return JSON.parse(metafields[0].value);
  }

  return [];
}

export async function saveAnalyticsData(session: ShopifySession, data: AnalyticsData[]): Promise<void> {
  const service = new MetafieldService(session);
  await service.upsert({
    namespace: 'bundleforge',
    key: 'analytics_all',
    value: JSON.stringify(data),
    type: 'json',
  });
}

export async function updateBundleAnalytics(session: ShopifySession, bundleId: string, updates: Partial<AnalyticsData>): Promise<void> {
  const allAnalytics = await getAnalyticsData(session);
  const existingIndex = allAnalytics.findIndex(a => a.bundle_id === bundleId);

  if (existingIndex >= 0) {
    allAnalytics[existingIndex] = {
      ...allAnalytics[existingIndex],
      ...updates,
      last_updated: new Date().toISOString()
    };
  } else {
    allAnalytics.push({
      bundle_id: bundleId,
      conversions: 0,
      revenue: 0,
      views: 0,
      cart_adds: 0,
      conversion_funnel: {
        views: 0,
        cart_adds: 0,
        checkouts: 0,
        purchases: 0,
      },
      ab_test_data: {},
      ...updates,
      last_updated: new Date().toISOString(),
    });
  }

  await saveAnalyticsData(session, allAnalytics);
}

// Utility functions
export function generateBundleId(): string {
  return `bundle_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}
