import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  Page,
  Card,
  FormLayout,
  TextField,
  Button,
  ResourceList,
  ResourceItem,
  Text,
  Badge,
  Banner,
  ButtonGroup,
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { PlusIcon, DeleteIcon } from "@shopify/polaris-icons";
import { requireAuth } from "../libs/auth.server";
import { BundleService } from "../libs/bundle.server";

// Mock data for MMVP
const mockProducts = [
  { id: "1", title: "Product A", price: "29.99", image: null },
  { id: "2", title: "Product B", price: "39.99", image: null },
  { id: "3", title: "Product C", price: "19.99", image: null },
  { id: "4", title: "Product D", price: "49.99", image: null },
  { id: "5", title: "Product E", price: "24.99", image: null },
];

const mockBundles = [
  {
    id: "1",
    name: "Summer Essentials",
    status: "active",
    products: [
      { product_id: "1", quantity: 2 },
      { product_id: "2", quantity: 1 }
    ],
    discount: "15%",
    discount_percentage: 15,
    created: "2024-01-15",
    active: true,
  },
  {
    id: "2",
    name: "Winter Bundle",
    status: "draft",
    products: [
      { product_id: "3", quantity: 1 },
      { product_id: "4", quantity: 1 }
    ],
    discount: "20%",
    discount_percentage: 20,
    created: "2024-01-18",
    active: false,
  },
];

export async function loader({ params }: LoaderFunctionArgs) {
  const bundleId = params.id;
  
  // In a real app, fetch from Shopify metafields
  const bundle = mockBundles.find(b => b.id === bundleId);
  
  if (!bundle) {
    throw new Response("Bundle not found", { status: 404 });
  }
  
  return json({ bundle, products: mockProducts });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const bundleId = params.id;
  const formData = await request.formData();
  const intent = formData.get("intent") as string;
  
  if (intent === "delete") {
    // In a real app, delete from Shopify metafields and ghost product
    console.log("Deleting bundle:", bundleId);
    await new Promise(resolve => setTimeout(resolve, 500));
    return redirect("/bundles");
  }
  
  const name = formData.get("name") as string;
  const discountPercentage = formData.get("discountPercentage") as string;
  const products = formData.get("products") as string;
  const active = formData.get("active") === "true";

  // Validation
  const errors: Record<string, string> = {};
  
  if (!name || name.trim().length === 0) {
    errors.name = "Bundle name is required";
  }
  
  if (!discountPercentage || isNaN(Number(discountPercentage)) || Number(discountPercentage) < 0 || Number(discountPercentage) > 100) {
    errors.discountPercentage = "Discount percentage must be between 0 and 100";
  }
  
  let parsedProducts;
  try {
    parsedProducts = JSON.parse(products || "[]");
    if (!Array.isArray(parsedProducts) || parsedProducts.length === 0) {
      errors.products = "At least one product is required";
    }
  } catch {
    errors.products = "Invalid products data";
  }
  
  if (Object.keys(errors).length > 0) {
    return json({ errors }, { status: 400 });
  }

  // In a real app, update Shopify metafields and ghost product
  console.log("Updating bundle:", {
    id: bundleId,
    name,
    discountPercentage: Number(discountPercentage),
    products: parsedProducts,
    active,
  });

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  return redirect("/bundles");
}

interface BundleProduct {
  product_id: string;
  quantity: number;
}

export default function EditBundle() {
  const { bundle, products } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  const isDeleting = navigation.formData?.get("intent") === "delete";

  const [name, setName] = useState(bundle.name);
  const [discountPercentage, setDiscountPercentage] = useState(bundle.discount_percentage.toString());
  const [selectedProducts, setSelectedProducts] = useState<BundleProduct[]>(bundle.products);
  const [active, setActive] = useState(bundle.active);
  const [showProductPicker, setShowProductPicker] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleNameChange = useCallback((value: string) => {
    setName(value);
  }, []);

  const handleDiscountChange = useCallback((value: string) => {
    setDiscountPercentage(value);
  }, []);

  const handleAddProduct = useCallback((productId: string) => {
    const existingProduct = selectedProducts.find(p => p.product_id === productId);
    if (existingProduct) {
      setSelectedProducts(prev => 
        prev.map(p => 
          p.product_id === productId 
            ? { ...p, quantity: p.quantity + 1 }
            : p
        )
      );
    } else {
      setSelectedProducts(prev => [...prev, { product_id: productId, quantity: 1 }]);
    }
    setShowProductPicker(false);
  }, [selectedProducts]);

  const handleRemoveProduct = useCallback((productId: string) => {
    setSelectedProducts(prev => prev.filter(p => p.product_id !== productId));
  }, []);

  const handleQuantityChange = useCallback((productId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveProduct(productId);
      return;
    }
    setSelectedProducts(prev => 
      prev.map(p => 
        p.product_id === productId 
          ? { ...p, quantity }
          : p
      )
    );
  }, [handleRemoveProduct]);

  const getProductById = (id: string) => {
    return products.find(p => p.id === id);
  };

  const availableProducts = products.filter(
    p => !selectedProducts.some(sp => sp.product_id === p.id)
  );

  return (
    <Page
      title={`Edit Bundle: ${bundle.name}`}
      backAction={{ url: "/bundles" }}
      primaryAction={{
        content: isSubmitting && !isDeleting ? "Saving..." : "Save Bundle",
        loading: isSubmitting && !isDeleting,
        disabled: isSubmitting,
        onAction: () => {
          const form = document.getElementById("bundle-form") as HTMLFormElement;
          form?.requestSubmit();
        },
      }}
      secondaryActions={[
        {
          content: "Delete Bundle",
          destructive: true,
          onAction: () => setShowDeleteConfirm(true),
        },
      ]}
    >
      {actionData?.errors && (
        <Banner status="critical" title="Please fix the following errors:">
          <ul>
            {Object.entries(actionData.errors).map(([field, error]) => (
              <li key={field}>{error}</li>
            ))}
          </ul>
        </Banner>
      )}

      {showDeleteConfirm && (
        <Banner
          status="critical"
          title="Delete Bundle"
          action={{
            content: isDeleting ? "Deleting..." : "Delete",
            loading: isDeleting,
            onAction: () => {
              const form = document.getElementById("delete-form") as HTMLFormElement;
              form?.requestSubmit();
            },
          }}
          secondaryAction={{
            content: "Cancel",
            onAction: () => setShowDeleteConfirm(false),
          }}
        >
          <p>Are you sure you want to delete this bundle? This action cannot be undone.</p>
        </Banner>
      )}

      <Form method="post" id="bundle-form">
        <FormLayout>
          <Card>
            <FormLayout>
              <TextField
                label="Bundle Name"
                value={name}
                onChange={handleNameChange}
                error={actionData?.errors?.name}
                placeholder="e.g., Summer Essentials Bundle"
                autoComplete="off"
              />
              
              <TextField
                label="Discount Percentage"
                type="number"
                value={discountPercentage}
                onChange={handleDiscountChange}
                error={actionData?.errors?.discountPercentage}
                suffix="%"
                min="0"
                max="100"
                step="1"
                helpText="Percentage discount applied to the total bundle price"
              />
              
              <div>
                <Text variant="bodyMd" as="p">
                  Status: {" "}
                  <Badge tone={active ? "success" : "info"}>
                    {active ? "Active" : "Inactive"}
                  </Badge>
                </Text>
                <div style={{ marginTop: "0.5rem" }}>
                  <Button
                    onClick={() => setActive(!active)}
                    variant={active ? "tertiary" : "primary"}
                    tone={active ? "critical" : "success"}
                  >
                    {active ? "Deactivate" : "Activate"} Bundle
                  </Button>
                </div>
              </div>
            </FormLayout>
          </Card>

          <Card>
            <div style={{ marginBottom: "1rem" }}>
              <Text variant="headingMd" as="h2">
                Bundle Products
              </Text>
              <Text variant="bodyMd" as="p" color="subdued">
                Manage products in this bundle
              </Text>
            </div>

            {selectedProducts.length > 0 && (
              <div style={{ marginBottom: "1rem" }}>
                <ResourceList
                  resourceName={{ singular: "product", plural: "products" }}
                  items={selectedProducts.map(sp => {
                    const product = getProductById(sp.product_id);
                    return {
                      id: sp.product_id,
                      product,
                      quantity: sp.quantity,
                    };
                  })}
                  renderItem={(item) => {
                    const { id, product, quantity } = item;
                    if (!product) return null;

                    return (
                      <ResourceItem
                        id={id}
                        media={
                          <div
                            style={{
                              width: "40px",
                              height: "40px",
                              backgroundColor: "#f6f6f7",
                              borderRadius: "4px",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text variant="bodySm" color="subdued">
                              IMG
                            </Text>
                          </div>
                        }
                      >
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                          <div>
                            <Text variant="bodyMd" fontWeight="medium">
                              {product.title}
                            </Text>
                            <Text variant="bodySm" color="subdued">
                              ${product.price}
                            </Text>
                          </div>
                          <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
                            <TextField
                              label="Quantity"
                              labelHidden
                              type="number"
                              value={quantity.toString()}
                              onChange={(value) => handleQuantityChange(id, parseInt(value) || 0)}
                              min="1"
                              style={{ width: "80px" }}
                            />
                            <Button
                              icon={DeleteIcon}
                              variant="tertiary"
                              tone="critical"
                              onClick={() => handleRemoveProduct(id)}
                              accessibilityLabel={`Remove ${product.title}`}
                            />
                          </div>
                        </div>
                      </ResourceItem>
                    );
                  }}
                />
              </div>
            )}

            {!showProductPicker ? (
              <Button
                icon={PlusIcon}
                onClick={() => setShowProductPicker(true)}
                disabled={availableProducts.length === 0}
              >
                Add Product
              </Button>
            ) : (
              <Card>
                <div style={{ marginBottom: "1rem" }}>
                  <Text variant="headingSm" as="h3">
                    Select a Product
                  </Text>
                </div>
                <ResourceList
                  resourceName={{ singular: "product", plural: "products" }}
                  items={availableProducts}
                  renderItem={(product) => (
                    <ResourceItem
                      id={product.id}
                      media={
                        <div
                          style={{
                            width: "40px",
                            height: "40px",
                            backgroundColor: "#f6f6f7",
                            borderRadius: "4px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text variant="bodySm" color="subdued">
                            IMG
                          </Text>
                        </div>
                      }
                      onClick={() => handleAddProduct(product.id)}
                    >
                      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                        <div>
                          <Text variant="bodyMd" fontWeight="medium">
                            {product.title}
                          </Text>
                          <Text variant="bodySm" color="subdued">
                            ${product.price}
                          </Text>
                        </div>
                        <Button size="micro">Add</Button>
                      </div>
                    </ResourceItem>
                  )}
                />
                <div style={{ marginTop: "1rem" }}>
                  <Button onClick={() => setShowProductPicker(false)}>
                    Cancel
                  </Button>
                </div>
              </Card>
            )}

            {actionData?.errors?.products && (
              <div style={{ marginTop: "1rem" }}>
                <Text variant="bodySm" color="critical">
                  {actionData.errors.products}
                </Text>
              </div>
            )}
          </Card>
        </FormLayout>

        {/* Hidden inputs for form submission */}
        <input type="hidden" name="intent" value="update" />
        <input type="hidden" name="name" value={name} />
        <input type="hidden" name="discountPercentage" value={discountPercentage} />
        <input type="hidden" name="products" value={JSON.stringify(selectedProducts)} />
        <input type="hidden" name="active" value={active.toString()} />
      </Form>

      {/* Delete form */}
      <Form method="post" id="delete-form" style={{ display: "none" }}>
        <input type="hidden" name="intent" value="delete" />
      </Form>
    </Page>
  );
}