import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { verifyWebhook } from "../libs/auth.server";
import { OrderWebhookSchema } from "../libs/validation.server";
import { handleError } from "../libs/error-handler.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const orderData = JSON.parse(rawBody);
    
    // Validate webhook data
    const order = OrderWebhookSchema.parse(orderData);
    const shop = request.headers.get("x-shopify-shop-domain");
    
    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Process order for bundle analytics
    await processOrderForBundleAnalytics(order, shop);
    
    // Log order creation without sensitive data
    console.log('Order created webhook processed', {
      timestamp: new Date().toISOString(),
      lineItemsCount: order.line_items?.length || 0,
      // Don't log shop, order IDs, or prices
    });

    return json({ success: true }, { status: 200 });
  } catch (error) {
    return handleError(error);
  }
}

// Extract shop domain from order data
function extractShopFromOrder(order: any): string | null {
  // Try different possible locations for shop domain
  return order.shop_domain || 
         order.referring_site?.match(/https?:\/\/([^.]+)\.myshopify\.com/)?.[1] ||
         null;
}

// Process order for bundle analytics
async function processOrderForBundleAnalytics(order: any, shop: string): Promise<void> {
  try {
    const bundleLineItems = await identifyBundleLineItems(order.line_items || []);
    
    if (bundleLineItems.length === 0) {
      return; // No bundle items in this order
    }

    // Track bundle performance metrics
    for (const bundleItem of bundleLineItems) {
      await trackBundleConversion({
        shop,
        orderId: order.id,
        orderNumber: order.order_number,
        bundleId: bundleItem.bundleId,
        bundleType: bundleItem.bundleType,
        productId: bundleItem.product_id,
        variantId: bundleItem.variant_id,
        quantity: bundleItem.quantity,
        price: bundleItem.price,
        discountAmount: bundleItem.discountAmount,
        customerInfo: {
          id: order.customer?.id,
          email: order.customer?.email,
          isReturning: order.customer?.orders_count > 1,
        },
        orderInfo: {
          totalPrice: order.total_price,
          currency: order.currency,
          createdAt: order.created_at,
          tags: order.tags,
        },
      });
    }

    // Track cross-sell and upsell effectiveness
    await trackCrossSellUpsellMetrics(order, bundleLineItems, shop);
    
    // Update A/B testing metrics
    await updateABTestMetrics(order, bundleLineItems, shop);
    
  } catch (error) {
    console.error(`Error processing bundle analytics for order ${order.id}:`, error);
  }
}

// Identify bundle line items from order
async function identifyBundleLineItems(lineItems: any[]): Promise<any[]> {
  const bundleItems = [];
  
  for (const item of lineItems) {
    // Check if line item has bundle metadata
    const bundleMetadata = item.properties?.find(
      (prop: any) => prop.name === '_bundle_id' || prop.name === 'bundle_id'
    );
    
    if (bundleMetadata) {
      // Extract bundle information from line item properties
      const bundleId = bundleMetadata.value;
      const bundleType = item.properties?.find(
        (prop: any) => prop.name === '_bundle_type' || prop.name === 'bundle_type'
      )?.value || 'unknown';
      
      const discountAmount = calculateBundleDiscount(item);
      
      bundleItems.push({
        ...item,
        bundleId,
        bundleType,
        discountAmount,
      });
    }
  }
  
  return bundleItems;
}

// Calculate bundle discount amount
function calculateBundleDiscount(lineItem: any): number {
  const originalPrice = parseFloat(lineItem.compare_at_price || lineItem.price);
  const salePrice = parseFloat(lineItem.price);
  const quantity = parseInt(lineItem.quantity);
  
  return (originalPrice - salePrice) * quantity;
}

// Track bundle conversion metrics
async function trackBundleConversion(data: any): Promise<void> {
  // TODO: Implement actual analytics tracking
  // This would typically involve:
  // 1. Storing conversion data in analytics database
  // 2. Updating bundle performance metrics
  // 3. Calculating conversion rates
  // 4. Updating revenue attribution
  
  console.log('Bundle conversion tracked', {
    timestamp: new Date().toISOString(),
    // Don't log specific IDs or financial data
  });
}

// Track cross-sell and upsell metrics
async function trackCrossSellUpsellMetrics(
  order: any, 
  bundleItems: any[], 
  shop: string
): Promise<void> {
  // TODO: Implement cross-sell/upsell tracking
  // Analyze if bundle items led to additional purchases
  
  const totalBundleValue = bundleItems.reduce(
    (sum, item) => sum + (parseFloat(item.price) * parseInt(item.quantity)), 
    0
  );
  
  const totalOrderValue = parseFloat(order.total_price);
  const additionalValue = totalOrderValue - totalBundleValue;
  
  if (additionalValue > 0) {
    console.log('Cross-sell/upsell detected', {
      upliftPercentage: (additionalValue / totalBundleValue) * 100,
      // Don't log shop or financial values
    });
  }
}

// Update A/B testing metrics
async function updateABTestMetrics(
  order: any, 
  bundleItems: any[], 
  shop: string
): Promise<void> {
  // TODO: Implement A/B testing metrics update
  // Check if order contains items from A/B tested bundles
  
  for (const item of bundleItems) {
    // Look for A/B test identifiers in line item properties
    const abTestId = item.properties?.find(
      (prop: any) => prop.name === '_ab_test_id'
    )?.value;
    
    const abTestVariant = item.properties?.find(
      (prop: any) => prop.name === '_ab_test_variant'
    )?.value;
    
    if (abTestId && abTestVariant) {
      console.log('A/B test conversion detected', {
        testId: abTestId,
        variant: abTestVariant,
        // Don't log shop or revenue data
      });
    }
  }
}