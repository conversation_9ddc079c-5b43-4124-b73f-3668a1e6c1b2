import { json, type ActionFunctionArgs } from '@remix-run/node';
import { z } from 'zod';
import { AnalyticsService } from '~/libs/analytics.server';
import { BundleService } from '~/libs/bundle.server';
import { verifyWebhookSignature } from '~/libs/security-webhooks.server';
import crypto from 'crypto';

// Event schema for validation
const TrackEventSchema = z.object({
  event_type: z.enum(['bundle_view', 'bundle_add_to_cart', 'bundle_purchase', 'ab_test_exposure']),
  bundle_id: z.string(),
  customer_id: z.string().optional(),
  session_id: z.string(),
  revenue: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

// CORS headers for cross-origin requests from storefront
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, X-Shopify-Shop, X-Shopify-Customer-Id',
};

export const action = async ({ request }: ActionFunctionArgs) => {
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, { 
      status: 204,
      headers: corsHeaders,
    });
  }
  
  try {
    // Get shop domain from header
    const shopDomain = request.headers.get('X-Shopify-Shop');
    if (!shopDomain) {
      return json(
        { error: 'Missing shop domain' },
        { status: 400, headers: corsHeaders }
      );
    }
    
    // Parse and validate request body
    const body = await request.json();
    const validatedData = TrackEventSchema.parse(body);
    
    // Initialize services
    const analyticsService = new AnalyticsService(shopDomain);
    
    // Get customer ID from header or use anonymous ID
    const customerId = request.headers.get('X-Shopify-Customer-Id') || 
                      validatedData.customer_id || 
                      `anon_${validatedData.session_id}`;
    
    // Check if this is part of an A/B test
    let abTestId: string | undefined;
    let abTestVariant: 'variant_a' | 'variant_b' | undefined;
    
    if (validatedData.metadata?.ab_test_enabled) {
      // Get bundle to check A/B test configuration
      const bundleService = new BundleService(shopDomain, ''); // We don't need GraphQL for this
      const bundles = await bundleService.getAllBundlesLegacy();
      const bundle = bundles.find(b => b.id === validatedData.bundle_id);
      
      if (bundle?.ab_test_config?.enabled && bundle.ab_test_config.test_id) {
        abTestId = bundle.ab_test_config.test_id;
        // Get variant assignment for this customer
        abTestVariant = await analyticsService.getVariantAssignment(customerId, abTestId);
      }
    }
    
    // Track the event
    await analyticsService.trackEvent({
      event_type: validatedData.event_type,
      bundle_id: validatedData.bundle_id,
      customer_id: customerId,
      session_id: validatedData.session_id,
      ab_test_id: abTestId,
      ab_test_variant: abTestVariant,
      revenue: validatedData.revenue,
      metadata: {
        ...validatedData.metadata,
        source: 'storefront_api',
        tracked_at: new Date().toISOString(),
      },
    });
    
    // Return variant assignment if this is an A/B test
    const response: any = { success: true };
    if (abTestId && abTestVariant) {
      response.ab_test = {
        test_id: abTestId,
        variant: abTestVariant,
      };
    }
    
    return json(response, { headers: corsHeaders });
  } catch (error) {
    console.error('Analytics tracking error:', error);
    
    if (error instanceof z.ZodError) {
      return json(
        { error: 'Invalid event data', details: error.errors },
        { status: 400, headers: corsHeaders }
      );
    }
    
    return json(
      { error: 'Failed to track event' },
      { status: 500, headers: corsHeaders }
    );
  }
};

// Loader to handle GET requests (return method not allowed)
export const loader = async () => {
  return json(
    { error: 'Method not allowed' },
    { status: 405, headers: corsHeaders }
  );
};