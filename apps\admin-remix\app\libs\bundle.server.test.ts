import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BundleService } from './bundle.server';
import { createShopifyClient } from './shopify.server';
import { cache } from './cache.server';

// Mock dependencies
vi.mock('./shopify.server', () => ({
  createShopifyClient: vi.fn(() => ({
    request: vi.fn(),
  })),
}));

vi.mock('./cache.server', () => ({
  cache: {
    withCache: vi.fn((key, fetcher) => fetcher()),
    invalidatePattern: vi.fn(),
  },
}));

describe('BundleService', () => {
  let bundleService: BundleService;
  let mockClient: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockClient = {
      request: vi.fn(),
    };
    (createShopifyClient as any).mockReturnValue(mockClient);
    bundleService = new BundleService('test-shop.myshopify.com', 'test-token');
  });
  
  describe('createBundle', () => {
    it('should create a bundle with proper ID and timestamps', async () => {
      mockClient.request.mockResolvedValueOnce({
        data: {
          metafieldsSet: {
            metafields: [{ id: 'gid://shopify/Metafield/123' }],
            userErrors: [],
          },
        },
      });
      
      const bundle = await bundleService.createBundle({
        name: 'Test Bundle',
        products: [
          { product_id: 'gid://shopify/Product/1', quantity: 2 },
          { product_id: 'gid://shopify/Product/2', quantity: 1 },
        ],
        discount_percentage: 10,
        active: true,
      });
      
      expect(bundle).toMatchObject({
        name: 'Test Bundle',
        discount_percentage: 10,
        active: true,
        shop_domain: 'test-shop.myshopify.com',
      });
      expect(bundle.id).toMatch(/^bundle_\d+_[a-z0-9]+$/);
      expect(bundle.created_at).toBeDefined();
      expect(bundle.updated_at).toBeDefined();
    });
    
    it('should throw error on Shopify API failure', async () => {
      mockClient.request.mockResolvedValueOnce({
        data: {
          metafieldsSet: {
            userErrors: [{ field: 'value', message: 'Invalid JSON' }],
          },
        },
      });
      
      await expect(
        bundleService.createBundle({
          name: 'Test Bundle',
          products: [],
          discount_percentage: 10,
          active: true,
        })
      ).rejects.toThrow('Failed to create bundle: Invalid JSON');
    });
  });
  
  describe('getBundleById', () => {
    it('should return bundle if found', async () => {
      const mockBundle = {
        id: 'bundle_123',
        name: 'Test Bundle',
        products: [],
        discount_percentage: 15,
        active: true,
      };
      
      mockClient.request.mockResolvedValueOnce({
        data: {
          shop: {
            metafield: {
              value: JSON.stringify(mockBundle),
            },
          },
        },
      });
      
      const bundle = await bundleService.getBundleById('bundle_123');
      expect(bundle).toEqual(mockBundle);
    });
    
    it('should return null if bundle not found', async () => {
      mockClient.request.mockResolvedValueOnce({
        data: {
          shop: {
            metafield: null,
          },
        },
      });
      
      const bundle = await bundleService.getBundleById('non-existent');
      expect(bundle).toBeNull();
    });
  });
  
  describe('getBundlesPaginated', () => {
    it('should parse metafields and return paginated bundles', async () => {
      const mockBundles = [
        {
          id: 'bundle_1',
          name: 'Bundle 1',
          products: [],
          discount_percentage: 10,
          active: true,
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'bundle_2',
          name: 'Bundle 2',
          products: [],
          discount_percentage: 20,
          active: false,
          created_at: '2024-01-02T00:00:00Z',
        },
      ];
      
      mockClient.request.mockResolvedValueOnce({
        data: {
          shop: {
            metafields: {
              edges: mockBundles.map((bundle, i) => ({
                node: {
                  key: `bundle_config_${bundle.id}`,
                  value: JSON.stringify(bundle),
                },
                cursor: `cursor_${i}`,
              })),
              pageInfo: {
                hasNextPage: true,
                endCursor: 'cursor_1',
              },
            },
          },
        },
      });
      
      const result = await bundleService.getBundlesPaginated({ first: 2 });
      
      expect(result.bundles).toHaveLength(2);
      expect(result.bundles[0].id).toBe('bundle_2'); // Sorted by date desc
      expect(result.bundles[1].id).toBe('bundle_1');
      expect(result.pageInfo).toEqual({
        hasNextPage: true,
        endCursor: 'cursor_1',
      });
    });
    
    it('should filter active bundles when activeOnly is true', async () => {
      const mockBundles = [
        {
          id: 'bundle_1',
          name: 'Active Bundle',
          products: [],
          discount_percentage: 10,
          active: true,
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'bundle_2',
          name: 'Inactive Bundle',
          products: [],
          discount_percentage: 20,
          active: false,
          created_at: '2024-01-02T00:00:00Z',
        },
      ];
      
      mockClient.request.mockResolvedValueOnce({
        data: {
          shop: {
            metafields: {
              edges: mockBundles.map((bundle) => ({
                node: {
                  key: `bundle_config_${bundle.id}`,
                  value: JSON.stringify(bundle),
                },
              })),
              pageInfo: {
                hasNextPage: false,
                endCursor: null,
              },
            },
          },
        },
      });
      
      const result = await bundleService.getBundlesPaginated({ activeOnly: true });
      
      expect(result.bundles).toHaveLength(1);
      expect(result.bundles[0].id).toBe('bundle_1');
    });
  });
  
  describe('deleteBundlesBatch', () => {
    it('should delete multiple bundles in batches', async () => {
      const bundleIds = ['bundle_1', 'bundle_2', 'bundle_3'];
      
      // Mock metafield ID queries
      bundleIds.forEach((id, index) => {
        mockClient.request.mockResolvedValueOnce({
          data: {
            shop: {
              metafield: {
                id: `gid://shopify/Metafield/${index + 1}`,
              },
            },
          },
        });
      });
      
      // Mock deletion responses
      mockClient.request.mockResolvedValue({
        data: {
          metafieldDelete: {
            deletedId: 'test',
            userErrors: [],
          },
        },
      });
      
      await bundleService.deleteBundlesBatch(bundleIds);
      
      // Should query for each bundle ID
      expect(mockClient.request).toHaveBeenCalledTimes(
        bundleIds.length + // ID queries
        bundleIds.length + // Delete mutations
        1 // Sync call
      );
    });
  });
  
  describe('syncBundlesToShopMetafields', () => {
    it('should sync active bundles to shop metafields', async () => {
      const mockBundles = [
        {
          id: 'bundle_1',
          name: 'Active Bundle',
          products: [],
          discount_percentage: 10,
          active: true,
        },
        {
          id: 'bundle_2',
          name: 'Inactive Bundle',
          products: [],
          discount_percentage: 20,
          active: false,
        },
      ];
      
      // Mock getAllBundles response
      vi.spyOn(bundleService, 'getAllBundlesLegacy').mockResolvedValueOnce(mockBundles);
      
      mockClient.request.mockResolvedValueOnce({
        data: {
          metafieldsSet: {
            metafields: [{ id: 'gid://shopify/Metafield/sync' }],
            userErrors: [],
          },
        },
      });
      
      await (bundleService as any).syncBundlesToShopMetafields();
      
      // Verify only active bundles are synced
      expect(mockClient.request).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          variables: expect.objectContaining({
            metafields: expect.arrayContaining([
              expect.objectContaining({
                key: 'active_bundles',
                value: expect.stringContaining('"bundles":[{"id":"bundle_1"'),
              }),
            ]),
          }),
        })
      );
    });
  });
});