import type { ActionFunctionArgs } from "@remix-run/node";
import { verifyWebhook, handleDataRequest } from "../libs/auth.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  // Get raw body for HMAC verification
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256");
  
  if (!hmacHeader) {
    return new Response("Missing HMAC header", { status: 401 });
  }

  // Verify webhook authenticity
  if (!verifyWebhook(rawBody, hmacHeader)) {
    return new Response("Invalid HMAC", { status: 401 });
  }

  try {
    const payload = JSON.parse(rawBody);
    const shop = payload.shop_domain;
    const customerId = payload.customer?.id?.toString();
    const customerEmail = payload.customer?.email;
    
    if (!shop) {
      return new Response("Missing shop domain", { status: 400 });
    }

    // Handle data request
    const customerData = await handleDataRequest(shop, customerId);
    
    // Log data request for compliance tracking
    console.log(`GDPR data request for shop: ${shop}`, {
      timestamp: new Date().toISOString(),
      shop,
      customerId,
      customerEmail,
      requestId: payload.data_request?.id,
    });

    // In a real implementation, you would:
    // 1. Collect all customer data from your systems
    // 2. Format it according to GDPR requirements
    // 3. Send it to the customer or make it available for download
    // 4. Log the completion of the request

    return Response.json({
      success: true,
      message: "Data request processed",
      data: customerData
    }, { status: 200 });
  } catch (error) {
    console.error("Error handling customer data request:", error);
    return new Response("Internal server error", { status: 500 });
  }
}