import { createApp, type AppBridgeConfig } from '@shopify/app-bridge';
import { authenticatedFetch } from '@shopify/app-bridge/utilities';
import { Redirect } from '@shopify/app-bridge/actions';

let appBridgeInstance: ReturnType<typeof createApp> | null = null;

export function initializeAppBridge(config: AppBridgeConfig) {
  if (!appBridgeInstance) {
    appBridgeInstance = createApp(config);
  }
  return appBridgeInstance;
}

export function getAppBridge() {
  if (!appBridgeInstance) {
    throw new Error('App Bridge not initialized. Call initializeAppBridge first.');
  }
  return appBridgeInstance;
}

export function getAuthenticatedFetch() {
  const app = getAppBridge();
  return authenticatedFetch(app);
}

export function redirectToAuth(shop: string) {
  const redirect = Redirect.create(getAppBridge());
  redirect.dispatch(Redirect.Action.REMOTE, `/auth?shop=${shop}`);
}

export function redirectToPage(path: string) {
  const redirect = Redirect.create(getAppBridge());
  redirect.dispatch(Redirect.Action.APP, path);
}

// Session token management
let sessionTokenPromise: Promise<string> | null = null;
let sessionTokenCache: { token: string; expires: number } | null = null;

export async function getSessionToken(forceRefresh = false): Promise<string> {
  const now = Date.now();
  
  // Return cached token if valid
  if (!forceRefresh && sessionTokenCache && sessionTokenCache.expires > now) {
    return sessionTokenCache.token;
  }
  
  // If already fetching, wait for that
  if (sessionTokenPromise && !forceRefresh) {
    return sessionTokenPromise;
  }
  
  // Fetch new token
  sessionTokenPromise = new Promise((resolve, reject) => {
    const app = getAppBridge();
    app.getState().then((state: any) => {
      if (state.sessionToken) {
        // Cache for 50 minutes (tokens expire in 60 minutes)
        sessionTokenCache = {
          token: state.sessionToken,
          expires: now + (50 * 60 * 1000)
        };
        resolve(state.sessionToken);
      } else {
        reject(new Error('No session token available'));
      }
    }).catch(reject);
  });
  
  try {
    const token = await sessionTokenPromise;
    return token;
  } finally {
    sessionTokenPromise = null;
  }
}

// Authenticated API client
export async function authenticatedApiCall(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const sessionToken = await getSessionToken();
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${sessionToken}`,
      'Content-Type': 'application/json',
    },
  });
  
  // If unauthorized, try refreshing token once
  if (response.status === 401) {
    const newToken = await getSessionToken(true);
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${newToken}`,
        'Content-Type': 'application/json',
      },
    });
  }
  
  return response;
}