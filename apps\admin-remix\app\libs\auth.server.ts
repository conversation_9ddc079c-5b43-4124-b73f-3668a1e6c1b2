import { redirect } from "@remix-run/node";
import { createShopifyClient, decodeSessionToken, validateSessionToken } from "./shopify.server";
import { sessionStorage } from "./session-storage.server";
import { getAppUrl, getOAuthCallbackUrl } from "./app-url.server";
import { getRedisClient } from './redis.server';
import { env } from './env-encryption.server';

// Use encrypted environment variables
const SHOPIFY_API_KEY = env.SHOPIFY_API_KEY;
const SHOPIFY_API_SECRET = env.SHOPIFY_API_SECRET;
const SHOPIFY_SCOPES = env.SHOPIFY_SCOPES;

// Session interface
export interface ShopifySession {
  shop: string;
  accessToken: string;
  scope: string;
  expires?: Date;
  isOnline: boolean;
  state?: string;
  onlineAccessInfo?: {
    expires_in: number;
    associated_user_scope: string;
    associated_user: {
      id: number;
      first_name: string;
      last_name: string;
      email: string;
      account_owner: boolean;
      locale: string;
      collaborator: boolean;
    };
  };
}

// OAuth URLs
export async function getAuthUrl(shop: string, state: string, request: Request): Promise<string> {
  // Save state for validation
  await saveOAuthState(state, shop);
  
  const redirectUri = getOAuthCallbackUrl(request);
  
  const params = new URLSearchParams({
    client_id: SHOPIFY_API_KEY,
    scope: SHOPIFY_SCOPES,
    redirect_uri: redirectUri,
    state,
    "grant_options[]": "per-user",
  });

  return `https://${shop}/admin/oauth/authorize?${params.toString()}`;
}

export function getInstallUrl(shop: string, request: Request): string {
  const redirectUri = getOAuthCallbackUrl(request);
  
  const params = new URLSearchParams({
    client_id: SHOPIFY_API_KEY,
    scope: SHOPIFY_SCOPES,
    redirect_uri: redirectUri,
  });

  return `https://${shop}/admin/oauth/authorize?${params.toString()}`;
}

// Token exchange
export async function exchangeCodeForToken(
  shop: string,
  code: string
): Promise<{
  access_token: string;
  scope: string;
  expires_in?: number;
  associated_user_scope?: string;
  associated_user?: any;
}> {
  const response = await fetch(`https://${shop}/admin/oauth/access_token`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      client_id: SHOPIFY_API_KEY,
      client_secret: SHOPIFY_API_SECRET,
      code,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to exchange code for token: ${response.statusText}`);
  }

  return response.json();
}

// Import Redis client for secure OAuth state storage
import { getRedisClient } from './redis.server';

// Session management functions
export async function saveSession(sessionId: string, session: ShopifySession): Promise<void> {
  await sessionStorage.save(sessionId, session);
}

export async function getSession(sessionId: string): Promise<ShopifySession | undefined> {
  return await sessionStorage.get(sessionId);
}

export async function deleteSession(sessionId: string): Promise<void> {
  await sessionStorage.delete(sessionId);
}

// OAuth state management with Redis for security
export async function saveOAuthState(state: string, shop: string): Promise<void> {
  try {
    const client = await getRedisClient();
    const key = `oauth:state:${state}`;
    const value = JSON.stringify({
      shop,
      timestamp: Date.now()
    });
    
    // Set with 10 minute TTL
    await client.setEx(key, 600, value);
  } catch (error) {
    console.error('Failed to save OAuth state:', error);
    throw new Error('OAuth state storage failed');
  }
}

export async function getOAuthState(state: string): Promise<{ shop: string; timestamp: number } | undefined> {
  try {
    const client = await getRedisClient();
    const key = `oauth:state:${state}`;
    const value = await client.get(key);
    
    if (!value) {
      return undefined;
    }
    
    return JSON.parse(value);
  } catch (error) {
    console.error('Failed to get OAuth state:', error);
    return undefined;
  }
}

export async function deleteOAuthState(state: string): Promise<void> {
  try {
    const client = await getRedisClient();
    const key = `oauth:state:${state}`;
    await client.del(key);
  } catch (error) {
    console.error('Failed to delete OAuth state:', error);
  }
}

export function generateSessionId(shop: string, userId?: string): string {
  const crypto = require("crypto");
  const timestamp = Date.now();
  const random = crypto.randomBytes(16).toString('hex');
  const baseId = userId ? `${shop}_${userId}` : `${shop}_offline`;
  // Add randomness to prevent session fixation
  return `${baseId}_${timestamp}_${random}`;
}

// Authentication middleware
export async function requireAuth(request: Request): Promise<{
  session: ShopifySession;
  client: ReturnType<typeof createShopifyClient>;
}> {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const sessionToken = request.headers.get("authorization")?.replace("Bearer ", "");

  // Check for session token (App Bridge)
  if (sessionToken) {
    try {
      if (!validateSessionToken(sessionToken, SHOPIFY_API_SECRET)) {
        throw new Error("Invalid session token");
      }

      const payload = decodeSessionToken(sessionToken);
      
      // Validate token expiration
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        throw new Error("Session token expired");
      }
      
      // Validate token not used before issued time
      if (payload.iat && payload.iat > now + 60) { // Allow 60 seconds clock skew
        throw new Error("Session token not yet valid");
      }
      
      // Validate audience and issuer
      const expectedAudience = SHOPIFY_API_KEY;
      if (payload.aud !== expectedAudience) {
        throw new Error("Invalid session token audience");
      }
      
      const sessionId = generateSessionId(payload.dest.replace("https://", ""), payload.sub);
      const session = await getSession(sessionId);

      if (!session) {
        throw new Error("Session not found");
      }

      // Check if session is expired
      if (session.expires && session.expires < new Date()) {
        await deleteSession(sessionId);
        throw new Error("Session expired");
      }

      const client = createShopifyClient(session.shop, session.accessToken);
      return { session, client };
    } catch (error) {
      console.error("Session token validation failed");
    }
  }

  // Check for shop parameter and redirect to auth
  if (shop) {
    const state = generateState();
    const authUrl = await getAuthUrl(shop, state, request);
    throw redirect(authUrl);
  }

  // No authentication method available
  throw new Response("Unauthorized", { status: 401 });
}

// Optional auth (doesn't throw if not authenticated)
export async function getAuth(request: Request): Promise<{
  session: ShopifySession;
  client: ReturnType<typeof createShopifyClient>;
} | null> {
  try {
    return await requireAuth(request);
  } catch (error) {
    return null;
  }
}

// Verify webhook authenticity
export function verifyWebhook(
  rawBody: string,
  signature: string
): boolean {
  const crypto = require("crypto");
  const hmac = crypto.createHmac("sha256", SHOPIFY_API_SECRET);
  hmac.update(rawBody, "utf8");
  const hash = hmac.digest("base64");
  return hash === signature;
}

// Generate secure state for OAuth
export function generateState(): string {
  const crypto = require("crypto");
  // Use 32 bytes for strong security
  return crypto.randomBytes(32).toString("base64url");
}

// Verify OAuth state with additional security checks
export async function verifyState(state: string, shop: string): Promise<boolean> {
  const storedState = await getOAuthState(state);
  
  if (!storedState) {
    return false;
  }
  
  // Verify shop matches
  if (storedState.shop !== shop) {
    return false;
  }
  
  // Verify state is not too old (10 minutes max)
  const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
  if (storedState.timestamp < tenMinutesAgo) {
    await deleteOAuthState(state);
    return false;
  }
  
  // State is valid, clean it up
  await deleteOAuthState(state);
  return true;
}

// HMAC verification for OAuth callback
export function verifyHmac(
  query: Record<string, string>,
  hmac: string
): boolean {
  const crypto = require("crypto");
  const { hmac: _, ...params } = query;
  
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join("&");
  
  const calculatedHmac = crypto
    .createHmac("sha256", SHOPIFY_API_SECRET)
    .update(sortedParams)
    .digest("hex");
  
  return calculatedHmac === hmac;
}

// App uninstallation handler
export async function handleAppUninstall(shop: string): Promise<void> {
  // Clean up all sessions for this shop
  await sessionStorage.deleteByShop(shop);
  
  // Log without sensitive data
  console.log(`App uninstalled for shop: [REDACTED]`);
}

// GDPR data request handler
export async function handleDataRequest(
  shop: string,
  customerId?: string
): Promise<any> {
  const shopSession = await getSession(generateSessionId(shop));
  
  if (!shopSession) {
    console.error(`No session found for shop: [REDACTED]`);
    return {
      shop,
      customerId,
      data: {}
    };
  }
  
  const client = createShopifyClient(shop, shopSession.accessToken);
  const customerData: any = {
    shop,
    customerId,
    data: {
      bundles: [],
      orderBundles: [],
      analytics: {}
    }
  };
  
  try {
    // Get all bundle configurations
    const bundleQuery = `
      query getShopBundles {
        shop {
          metafields(namespace: "bundleforge", first: 250) {
            edges {
              node {
                key
                value
                updatedAt
              }
            }
          }
        }
      }
    `;
    
    const bundleResponse = await client.request(bundleQuery);
    const bundles = bundleResponse.data?.shop?.metafields?.edges || [];
    
    // Filter for bundle configurations
    customerData.data.bundles = bundles
      .filter((edge: any) => edge.node.key.startsWith('bundle_config_'))
      .map((edge: any) => {
        try {
          const config = JSON.parse(edge.node.value);
          return {
            bundleId: config.id,
            bundleName: config.name,
            lastUpdated: edge.node.updatedAt
          };
        } catch (e) {
          return null;
        }
      })
      .filter(Boolean);
    
    // If customer ID provided, get customer-specific data
    if (customerId) {
      // Query orders containing bundles for this customer
      const orderQuery = `
        query getCustomerOrders($customerId: ID!) {
          customer(id: $customerId) {
            orders(first: 100) {
              edges {
                node {
                  id
                  createdAt
                  lineItems(first: 50) {
                    edges {
                      node {
                        title
                        customAttributes {
                          key
                          value
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;
      
      const orderResponse = await client.request(orderQuery, {
        variables: { customerId: `gid://shopify/Customer/${customerId}` }
      });
      
      const orders = orderResponse.data?.customer?.orders?.edges || [];
      
      // Extract bundle-related order data
      customerData.data.orderBundles = orders
        .map((order: any) => {
          const bundleItems = order.node.lineItems.edges
            .filter((item: any) => 
              item.node.customAttributes?.some((attr: any) => 
                attr.key === 'bundle_id' || attr.key === '_bundleforge'
              )
            )
            .map((item: any) => ({
              productTitle: item.node.title,
              bundleId: item.node.customAttributes.find((attr: any) => 
                attr.key === 'bundle_id'
              )?.value
            }));
          
          return bundleItems.length > 0 ? {
            orderId: order.node.id,
            orderDate: order.node.createdAt,
            bundleItems
          } : null;
        })
        .filter(Boolean);
      
      // Add analytics summary
      customerData.data.analytics = {
        totalBundleOrders: customerData.data.orderBundles.length,
        firstBundleOrder: customerData.data.orderBundles[0]?.orderDate || null,
        lastBundleOrder: customerData.data.orderBundles[customerData.data.orderBundles.length - 1]?.orderDate || null
      };
    }
  } catch (error) {
    console.error('Error collecting GDPR data:', error);
  }
  
  return customerData;
}

// Shopify CLI-style authenticate object for compatibility
export const authenticate = {
  admin: requireAuth,
  webhook: verifyWebhook,
};

// GDPR data deletion handler
export async function handleDataDeletion(
  shop: string,
  customerId?: string
): Promise<void> {
  console.log(`Processing data deletion request for shop: ${shop}, customer: ${customerId}`);
  
  const shopSession = await getSession(generateSessionId(shop));
  
  if (!shopSession) {
    console.error(`No session found for shop: [REDACTED]`);
    return;
  }
  
  const client = createShopifyClient(shop, shopSession.accessToken);
  
  try {
    if (customerId) {
      // Delete customer-specific metafields
      const customerMetafieldsQuery = `
        query getCustomerMetafields($customerId: ID!) {
          customer(id: $customerId) {
            metafields(namespace: "bundleforge", first: 100) {
              edges {
                node {
                  id
                  key
                }
              }
            }
          }
        }
      `;
      
      const metafieldsResponse = await client.request(customerMetafieldsQuery, {
        variables: { customerId: `gid://shopify/Customer/${customerId}` }
      });
      
      const metafields = metafieldsResponse.data?.customer?.metafields?.edges || [];
      
      // Delete each metafield
      for (const edge of metafields) {
        const deleteMutation = `
          mutation deleteMetafield($id: ID!) {
            metafieldDelete(input: { id: $id }) {
              userErrors {
                field
                message
              }
            }
          }
        `;
        
        await client.request(deleteMutation, {
          variables: { id: edge.node.id }
        });
      }
      
      console.log(`Deleted ${metafields.length} customer metafields`);
    } else {
      // Shop deletion - remove all bundle configurations
      const shopMetafieldsQuery = `
        query getShopMetafields {
          shop {
            metafields(namespace: "bundleforge", first: 250) {
              edges {
                node {
                  id
                  key
                }
              }
            }
          }
        }
      `;
      
      const metafieldsResponse = await client.request(shopMetafieldsQuery);
      const metafields = metafieldsResponse.data?.shop?.metafields?.edges || [];
      
      // Delete all shop metafields in batches
      const batchSize = 10;
      for (let i = 0; i < metafields.length; i += batchSize) {
        const batch = metafields.slice(i, i + batchSize);
        const deletePromises = batch.map((edge: any) => {
          const deleteMutation = `
            mutation deleteMetafield($id: ID!) {
              metafieldDelete(input: { id: $id }) {
                userErrors {
                  field
                  message
                }
              }
            }
          `;
          
          return client.request(deleteMutation, {
            variables: { id: edge.node.id }
          });
        });
        
        await Promise.all(deletePromises);
      }
      
      // Delete all sessions for this shop
      await sessionStorage.deleteByShop(shop);
      
      console.log(`Deleted all data for shop: ${metafields.length} metafields and all sessions`);
    }
  } catch (error) {
    console.error('Error deleting GDPR data');
    throw error;
  }
}