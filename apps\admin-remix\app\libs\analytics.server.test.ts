import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AnalyticsService } from './analytics.server';
import { MetafieldService } from './metafield.server';

// Mock dependencies
vi.mock('./metafield.server');
vi.mock('./cache.server', () => ({
  cache: {
    withCache: vi.fn((key, fetcher) => fetcher()),
    invalidatePattern: vi.fn(),
  },
}));

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let mockMetafieldService: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockMetafieldService = {
      setMetafield: vi.fn(),
      getMetafield: vi.fn(),
      getMetafieldsByNamespace: vi.fn(),
      deleteMetafield: vi.fn(),
    };
    
    // Mock the MetafieldService constructor
    vi.mocked(MetafieldService).mockImplementation(() => mockMetafieldService);
    
    analyticsService = new AnalyticsService('test-shop.myshopify.com');
  });
  
  describe('A/B Testing', () => {
    describe('createABTest', () => {
      it('should create a new A/B test with proper structure', async () => {
        mockMetafieldService.setMetafield.mockResolvedValueOnce(undefined);
        
        const testData = {
          name: 'Holiday Bundle Test',
          bundle_id: 'bundle_123',
          traffic_allocation: 50,
          variant_a: { name: 'Control', discount_percentage: 10 },
          variant_b: { name: 'Variant', discount_percentage: 15 },
          success_metric: 'conversion_rate' as const,
          minimum_sample_size: 1000,
          auto_end: false,
        };
        
        const abTest = await analyticsService.createABTest(testData);
        
        expect(abTest).toMatchObject({
          ...testData,
          status: 'draft',
          participants: 0,
          shop_domain: 'test-shop.myshopify.com',
        });
        expect(abTest.id).toMatch(/^ab_test_\d+_[a-z0-9]+$/);
        expect(abTest.created_at).toBeDefined();
        
        expect(mockMetafieldService.setMetafield).toHaveBeenCalledWith(
          'bundleforge_ab_tests',
          expect.stringContaining('ab_test_'),
          expect.any(String),
          'json'
        );
      });
    });
    
    describe('getVariantAssignment', () => {
      it('should return consistent variant assignment for same customer', async () => {
        // Mock no existing assignment
        mockMetafieldService.getMetafield.mockResolvedValueOnce(null);
        mockMetafieldService.setMetafield.mockResolvedValueOnce(undefined);
        
        // Mock the A/B test
        const mockTest = {
          id: 'ab_test_123',
          status: 'active',
          traffic_allocation: 50,
          participants: 0,
        };
        mockMetafieldService.getMetafieldsByNamespace.mockResolvedValueOnce([
          { value: JSON.stringify(mockTest) }
        ]);
        
        const customerId = 'customer_123';
        const testId = 'ab_test_123';
        
        const variant1 = await analyticsService.getVariantAssignment(customerId, testId);
        
        // Mock that assignment was saved
        mockMetafieldService.getMetafield.mockResolvedValueOnce({
          value: JSON.stringify({
            test_id: testId,
            variant: variant1,
            customer_id: customerId,
            assigned_at: new Date().toISOString(),
          })
        });
        
        const variant2 = await analyticsService.getVariantAssignment(customerId, testId);
        
        expect(variant1).toBe(variant2);
        expect(['variant_a', 'variant_b']).toContain(variant1);
      });
      
      it('should distribute customers roughly according to traffic allocation', async () => {
        // This is a statistical test, so we use a large sample
        const variantCounts = { variant_a: 0, variant_b: 0 };
        
        // Mock the A/B test with 70/30 split
        const mockTest = {
          id: 'ab_test_123',
          status: 'active',
          traffic_allocation: 70,
          participants: 0,
        };
        mockMetafieldService.getMetafieldsByNamespace.mockResolvedValue([
          { value: JSON.stringify(mockTest) }
        ]);
        mockMetafieldService.getMetafield.mockResolvedValue(null);
        mockMetafieldService.setMetafield.mockResolvedValue(undefined);
        
        // Test with 100 different customers
        for (let i = 0; i < 100; i++) {
          const variant = await analyticsService.getVariantAssignment(`customer_${i}`, 'ab_test_123');
          variantCounts[variant]++;
        }
        
        // With 70/30 split, we expect roughly 70 in variant_a
        // Allow for some statistical variance (±15%)
        expect(variantCounts.variant_a).toBeGreaterThan(55);
        expect(variantCounts.variant_a).toBeLessThan(85);
      });
    });
    
    describe('calculateABTestResults', () => {
      it('should calculate statistical significance correctly', async () => {
        const testId = 'ab_test_123';
        
        // Mock test data
        const mockTest = {
          id: testId,
          variant_a: { name: 'Control', discount_percentage: 10 },
          variant_b: { name: 'Variant', discount_percentage: 15 },
        };
        mockMetafieldService.getMetafieldsByNamespace
          .mockResolvedValueOnce([{ value: JSON.stringify(mockTest) }])
          .mockResolvedValueOnce([]); // No events yet
        
        // Mock events with clear winner
        const mockEvents = [
          // Variant A: 100 views, 10 conversions (10% conversion rate)
          ...Array(100).fill(null).map((_, i) => ({
            event_type: 'bundle_view',
            ab_test_variant: 'variant_a',
            ab_test_id: testId,
          })),
          ...Array(10).fill(null).map((_, i) => ({
            event_type: 'bundle_purchase',
            ab_test_variant: 'variant_a',
            ab_test_id: testId,
            revenue: 100,
          })),
          // Variant B: 100 views, 20 conversions (20% conversion rate)
          ...Array(100).fill(null).map((_, i) => ({
            event_type: 'bundle_view',
            ab_test_variant: 'variant_b',
            ab_test_id: testId,
          })),
          ...Array(20).fill(null).map((_, i) => ({
            event_type: 'bundle_purchase',
            ab_test_variant: 'variant_b',
            ab_test_id: testId,
            revenue: 100,
          })),
        ];
        
        mockMetafieldService.getMetafieldsByNamespace
          .mockResolvedValueOnce([{ value: JSON.stringify(mockTest) }])
          .mockResolvedValueOnce([{ value: JSON.stringify(mockEvents) }]);
        
        const results = await analyticsService.calculateABTestResults(testId);
        
        expect(results).toMatchObject({
          variant_a_views: 100,
          variant_a_conversions: 10,
          variant_a_revenue: 1000,
          variant_b_views: 100,
          variant_b_conversions: 20,
          variant_b_revenue: 2000,
          lift: 100, // 100% improvement
        });
        
        expect(results.statistical_significance).toBeGreaterThan(90);
        expect(results.winner).toBe('variant_b');
        expect(results.recommendation).toContain('improvement');
      });
      
      it('should handle no clear winner scenario', async () => {
        const testId = 'ab_test_123';
        
        // Mock test data
        const mockTest = {
          id: testId,
          variant_a: { name: 'Control', discount_percentage: 10 },
          variant_b: { name: 'Variant', discount_percentage: 15 },
        };
        mockMetafieldService.getMetafieldsByNamespace
          .mockResolvedValueOnce([{ value: JSON.stringify(mockTest) }])
          .mockResolvedValueOnce([]); // No events yet
        
        // Mock events with similar performance
        const mockEvents = [
          // Both variants: 10 views, 1 conversion each (10% conversion rate)
          ...Array(10).fill(null).map((_, i) => ({
            event_type: 'bundle_view',
            ab_test_variant: 'variant_a',
            ab_test_id: testId,
          })),
          {
            event_type: 'bundle_purchase',
            ab_test_variant: 'variant_a',
            ab_test_id: testId,
            revenue: 100,
          },
          ...Array(10).fill(null).map((_, i) => ({
            event_type: 'bundle_view',
            ab_test_variant: 'variant_b',
            ab_test_id: testId,
          })),
          {
            event_type: 'bundle_purchase',
            ab_test_variant: 'variant_b',
            ab_test_id: testId,
            revenue: 100,
          },
        ];
        
        mockMetafieldService.getMetafieldsByNamespace
          .mockResolvedValueOnce([{ value: JSON.stringify(mockTest) }])
          .mockResolvedValueOnce([{ value: JSON.stringify(mockEvents) }]);
        
        const results = await analyticsService.calculateABTestResults(testId);
        
        expect(results.statistical_significance).toBeLessThan(95);
        expect(results.winner).toBe('no_winner');
        expect(results.recommendation).toContain('not reached statistical significance');
      });
    });
  });
  
  describe('Analytics Events', () => {
    describe('trackEvent', () => {
      it('should track bundle view event', async () => {
        mockMetafieldService.getMetafield.mockResolvedValueOnce(null);
        mockMetafieldService.setMetafield.mockResolvedValueOnce(undefined);
        
        await analyticsService.trackEvent({
          event_type: 'bundle_view',
          bundle_id: 'bundle_123',
          session_id: 'sess_123',
          metadata: { page_url: 'https://shop.com/product' },
        });
        
        expect(mockMetafieldService.setMetafield).toHaveBeenCalledWith(
          'bundleforge_analytics',
          expect.stringContaining('events_bundle_123_'),
          expect.stringContaining('"event_type":"bundle_view"'),
          'json'
        );
      });
      
      it('should include A/B test data in events', async () => {
        mockMetafieldService.getMetafield.mockResolvedValueOnce(null);
        mockMetafieldService.setMetafield.mockResolvedValueOnce(undefined);
        
        await analyticsService.trackEvent({
          event_type: 'bundle_purchase',
          bundle_id: 'bundle_123',
          session_id: 'sess_123',
          ab_test_id: 'ab_test_456',
          ab_test_variant: 'variant_b',
          revenue: 99.99,
        });
        
        expect(mockMetafieldService.setMetafield).toHaveBeenCalledWith(
          'bundleforge_analytics',
          expect.any(String),
          expect.stringContaining('"ab_test_id":"ab_test_456"'),
          'json'
        );
      });
    });
    
    describe('getBundleAnalytics', () => {
      it('should aggregate bundle metrics correctly', async () => {
        const bundleId = 'bundle_123';
        
        // Mock events for multiple days
        const mockEvents = [
          // Day 1: 10 views, 2 cart adds, 1 purchase
          ...Array(10).fill(null).map(() => ({
            event_type: 'bundle_view',
            bundle_id: bundleId,
            timestamp: '2024-01-01T10:00:00Z',
          })),
          ...Array(2).fill(null).map(() => ({
            event_type: 'bundle_add_to_cart',
            bundle_id: bundleId,
            timestamp: '2024-01-01T11:00:00Z',
          })),
          {
            event_type: 'bundle_purchase',
            bundle_id: bundleId,
            revenue: 150,
            timestamp: '2024-01-01T12:00:00Z',
          },
          // Day 2: 5 views, 1 cart add, 0 purchases
          ...Array(5).fill(null).map(() => ({
            event_type: 'bundle_view',
            bundle_id: bundleId,
            timestamp: '2024-01-02T10:00:00Z',
          })),
          {
            event_type: 'bundle_add_to_cart',
            bundle_id: bundleId,
            timestamp: '2024-01-02T11:00:00Z',
          },
        ];
        
        mockMetafieldService.getMetafield
          .mockResolvedValueOnce({ value: JSON.stringify(mockEvents.filter(e => e.timestamp.includes('2024-01-01'))) })
          .mockResolvedValueOnce({ value: JSON.stringify(mockEvents.filter(e => e.timestamp.includes('2024-01-02'))) });
        
        const analytics = await analyticsService.getBundleAnalytics(bundleId, 2);
        
        expect(analytics.metrics).toMatchObject({
          views: 15,
          add_to_carts: 3,
          purchases: 1,
          revenue: 150,
          conversion_rate: expect.closeTo(6.67, 1), // 1/15 * 100
          cart_abandonment_rate: expect.closeTo(66.67, 1), // (3-1)/3 * 100
          average_order_value: 150,
        });
        
        expect(analytics.daily_breakdown).toHaveLength(2);
        expect(analytics.daily_breakdown[0]).toMatchObject({
          views: 10,
          add_to_carts: 2,
          purchases: 1,
          revenue: 150,
        });
      });
    });
  });
});