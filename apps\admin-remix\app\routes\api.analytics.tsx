import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../libs/auth.server";
import { 
  updateBundleAnalytics,
  getAnalyticsData,
  saveAnalyticsData,
  type AnalyticsData 
} from "../libs/metafield.server";
import { handleError } from "../libs/error-handler.server";
import { z } from "zod";

// Analytics event schema
const AnalyticsEventSchema = z.object({
  event_type: z.enum(['view', 'cart_add', 'purchase', 'remove_from_cart']),
  bundle_id: z.string(),
  product_ids: z.array(z.string()).optional(),
  revenue: z.number().optional(),
  customer_id: z.string().optional(),
  session_id: z.string().optional(),
  timestamp: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

type AnalyticsEvent = z.infer<typeof AnalyticsEventSchema>;

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const { session } = await authenticate.admin(request);
    const body = await request.json();
    
    // Validate the analytics event
    const event = AnalyticsEventSchema.parse(body);
    
    // Process the analytics event
    await processAnalyticsEvent(session, event);
    
    return json({ success: true });
  } catch (error) {
    console.error("Error processing analytics event:", error);
    return handleError(error);
  }
}

async function processAnalyticsEvent(session: any, event: AnalyticsEvent): Promise<void> {
  try {
    // Get existing analytics data for the bundle
    const existingAnalytics = await getAnalyticsData(session);
    const bundleAnalytics = existingAnalytics.find(a => a.bundle_id === event.bundle_id);
    
    // Create or update analytics data
    const updatedAnalytics: AnalyticsData = bundleAnalytics || {
      bundle_id: event.bundle_id,
      views: 0,
      cart_adds: 0,
      conversions: 0,
      revenue: 0,
      last_updated: new Date().toISOString(),
      conversion_funnel: {
        views: 0,
        cart_adds: 0,
        checkouts: 0,
        purchases: 0,
      },
      ab_test_data: {},
    };
    
    // Update metrics based on event type
    switch (event.event_type) {
      case 'view':
        updatedAnalytics.views += 1;
        updatedAnalytics.conversion_funnel.views += 1;
        break;
        
      case 'cart_add':
        updatedAnalytics.cart_adds += 1;
        updatedAnalytics.conversion_funnel.cart_adds += 1;
        break;
        
      case 'purchase':
        updatedAnalytics.conversions += 1;
        updatedAnalytics.conversion_funnel.purchases += 1;
        if (event.revenue) {
          updatedAnalytics.revenue += event.revenue;
        }
        break;
        
      case 'remove_from_cart':
        // Track cart abandonment (optional)
        break;
    }
    
    // Update timestamp
    updatedAnalytics.last_updated = new Date().toISOString();
    
    // Save updated analytics
    await updateBundleAnalytics(session, event.bundle_id, updatedAnalytics);
    
    console.log(`Processed ${event.event_type} event for bundle ${event.bundle_id}`);
  } catch (error) {
    console.error("Error processing analytics event:", error);
    throw error;
  }
}

// GET endpoint for retrieving analytics data
export async function loader({ request }: ActionFunctionArgs) {
  try {
    const { session } = await authenticate.admin(request);
    const url = new URL(request.url);
    const bundleId = url.searchParams.get('bundle_id');
    const timeRange = url.searchParams.get('time_range') || '30';
    
    let analytics = await getAnalyticsData(session);
    
    // Filter by bundle if specified
    if (bundleId && bundleId !== 'all') {
      analytics = analytics.filter(a => a.bundle_id === bundleId);
    }
    
    // TODO: Implement time range filtering
    // This would filter analytics based on the time_range parameter
    // For now, we return all data
    
    return json({
      analytics,
      summary: {
        total_revenue: analytics.reduce((sum, a) => sum + a.revenue, 0),
        total_conversions: analytics.reduce((sum, a) => sum + a.conversions, 0),
        total_views: analytics.reduce((sum, a) => sum + a.views, 0),
        total_cart_adds: analytics.reduce((sum, a) => sum + a.cart_adds, 0),
      }
    });
  } catch (error) {
    console.error("Error loading analytics data:", error);
    return handleError(error);
  }
}

// Utility function to track bundle view (can be called from other routes)
export async function trackBundleView(session: any, bundleId: string, metadata?: Record<string, any>) {
  try {
    const event: AnalyticsEvent = {
      event_type: 'view',
      bundle_id: bundleId,
      timestamp: new Date().toISOString(),
      metadata,
    };
    
    await processAnalyticsEvent(session, event);
  } catch (error) {
    console.error("Error tracking bundle view:", error);
    // Don't throw error to avoid breaking the main flow
  }
}

// Utility function to track cart add (can be called from other routes)
export async function trackBundleCartAdd(session: any, bundleId: string, productIds?: string[], metadata?: Record<string, any>) {
  try {
    const event: AnalyticsEvent = {
      event_type: 'cart_add',
      bundle_id: bundleId,
      product_ids: productIds,
      timestamp: new Date().toISOString(),
      metadata,
    };
    
    await processAnalyticsEvent(session, event);
  } catch (error) {
    console.error("Error tracking bundle cart add:", error);
    // Don't throw error to avoid breaking the main flow
  }
}

// Utility function to track purchase (can be called from webhooks)
export async function trackBundlePurchase(session: any, bundleId: string, revenue: number, productIds?: string[], customerId?: string, metadata?: Record<string, any>) {
  try {
    const event: AnalyticsEvent = {
      event_type: 'purchase',
      bundle_id: bundleId,
      product_ids: productIds,
      revenue,
      customer_id: customerId,
      timestamp: new Date().toISOString(),
      metadata,
    };
    
    await processAnalyticsEvent(session, event);
  } catch (error) {
    console.error("Error tracking bundle purchase:", error);
    // Don't throw error to avoid breaking the main flow
  }
}
